export default {
    listpg: {
        url: '/exammanagement/exam/listpg',
        auth: true,
        method: 'POST',
    },
    info:{
        url: '/exammanagement/exam/info',
        auth: true,
        method: 'POST',
    },
    uploadInfo:{
        url: '/exammanagement/exam/uploadInfo',
        auth: true,
        method: 'GET',
    },
    setScan: {
        url: '/exammanagement/exam/set_scan',
        auth: true,
        method: 'POST',
    },
    course:{
        gradeTypeCourseList: {
			url: '/schoolbasic/course/gradeTypeCourseList',
			auth: false,
			method: 'POST',
		},
        add:{
            url: '/exammanagement/course/add',
            auth: true,
            method: 'POST',
        },
        delete:{
            url: '/exammanagement/course/delete',
            auth: true,
            method: 'POST',
        },
    },
    code:{
        coding:{
            url: '/exammanagement/exam/examCode_coding',
            auth: true,
            method: 'POST',
        },
        info:{
            url: '/exammanagement/exam/examCode_info',
            auth: true,
            method: 'GET',
        }
    },
    class:{
        list:{
            url: '/exammanagement/class/list',
            auth: true,
            method: 'GET',
        },
        save:{
            url: '/exammanagement/class/save',
            auth: true,
            method: 'POST',
        }
    },
    room:{
        listpg:{
            url: '/exammanagement/room/listpg',
            auth: true,
            method: 'GET',
        },
        list:{
            url: '/exammanagement/room/list',
            auth: true,
            method: 'GET',
        },
        bp:{
            url: '/exammanagement/room/bp',
            auth: true,
            method: 'POST',
        },
        delete:{
            url: '/exammanagement/room/delete',
            auth: true,
            method: 'POST',
        },
        clear:{
            url: '/exammanagement/room/clear',
            auth: true,
            method: 'GET',
        }
    },
    student:{
        listpgByClass:{
            url: '/exammanagement/student/listpgByClass',
            auth: true,
            method: 'GET',
        },
        listpgByRoom:{
            url: '/exammanagement/student/listpgByExamRoom',
            auth: true,
            method: 'GET',
        },
        info:{
            url: '/exammanagement/student/info',
            auth: true,
            method: 'GET',
        },
        save:{
            url: '/exammanagement/student/save',
            auth: true,
            method: 'POST',
        },
        delete:{
            url: '/exammanagement/student/delete',
            auth: true,
            method: 'POST',
        }
    },
    template:{
        tk:{
            info:{
                url: '/exammanagement/template/tk',
                auth: true,
                method: 'GET',
            },
            save:{
                url: '/exammanagement/template/tk_save',
                auth: true,
                method: 'POST',
            },
        },
        kh:{
            info:{
                url: '/exammanagement/template/kh',
                auth: true,
                method: 'GET',
            },
            save:{
                url: '/exammanagement/template/kh_save',
                auth: true,
                method: 'POST',
            },
            delete:{
                url: '/exammanagement/template/kh_delete',
                auth: true,
                method: 'POST',
            },
        },
        xz:{
            info:{
                url: '/exammanagement/template/xz',
                auth: true,
                method: 'GET',
            },
            save:{
                url: '/exammanagement/template/xz_save',
                auth: true,
                method: 'POST',
            },
            delete:{
                url: '/exammanagement/template/xz_delete',
                auth: true,
                method: 'POST',
            },
        },
        zg:{
            info:{
                url: '/exammanagement/template/zg',
                auth: true,
                method: 'GET',
            },
            save:{
                url: '/exammanagement/template/zg_save',
                auth: true,
                method: 'POST',
            },
            delete:{
                url: '/exammanagement/template/zg_delete',
                auth: true,
                method: 'POST',
            },
            area:{
                save:{
                    url: '/exammanagement/template/zg_area_save',
                    auth: true,
                    method: 'POST',
                },
                delete:{
                    url: '/exammanagement/template/zg_area_delete',
                    auth: true,
                    method: 'POST',
                }
            },
            sub:{
                info:{
                    url: '/exammanagement/template/zg_sub',
                    auth: true,
                    method: 'GET',
                },
                save:{
                    url: '/exammanagement/template/zg_sub_save',
                    auth: true,
                    method: 'POST',
                },
                delete:{
                    url: '/exammanagement/template/zg_sub_delete',
                    auth: true,
                    method: 'POST',
                },
                area:{
                    save:{
                        url: '/exammanagement/template/zg_sub_area_save',
                        auth: true,
                        method: 'POST',
                    },
                    delete:{
                        url: '/exammanagement/template/zg_sub_area_delete',
                        auth: true,
                        method: 'POST',
                    }
                }
            }
        },
        sj:{
            info:{
                url: '/exammanagement/template/paper',
                auth: true,
                method: 'GET',
            },
            save:{
                url: '/exammanagement/template/paper_save',
                auth: true,
                method: 'POST',
            },
            area:{
                save:{
                    url: '/exammanagement/template/paper_area_save',
                    auth: true,
                    method: 'POST',
                },
                delete:{
                    url: '/exammanagement/template/paper_area_delete',
                    auth: true,
                    method: 'POST',
                },
            }

        },
        da:{
            info:{
                url: '/exammanagement/template/answer',
                auth: true,
                method: 'GET',
            },
            save:{
                url: '/exammanagement/template/answer_save',
                auth: true,
                method: 'POST',
            },
            area:{
                save:{
                    url: '/exammanagement/template/answer_area_save',
                    auth: true,
                    method: 'POST',
                },
                delete:{
                    url: '/exammanagement/template/answer_area_delete',
                    auth: true,
                    method: 'POST',
                },
            }

        }
    },
    recognition:{
        statistics:{
            url: '/exammanagement/recognition/statistics',
            auth: true,
            method: 'GET',
        },
        start:{
            url: '/exammanagement/recognition/start',
            auth: true,
            method: 'GET',
        },
        stop:{
            url: '/exammanagement/recognition/stop',
            auth: true,
            method: 'GET',
        },
        afresh:{
            all:{
                url: '/exammanagement/recognition/afresh',
                auth: true,
                method: 'GET',
            },
            km:{
                url: '/exammanagement/recognition/afresh_km',
                auth: true,
                method: 'GET',
            },
            kh:{
                url: '/exammanagement/recognition/afresh_kh',
                auth: true,
                method: 'GET',
            },
            xz:{
                url: '/exammanagement/recognition/afresh_xz',
                auth: true,
                method: 'GET',
            },
            zgxz:{
                url: '/exammanagement/recognition/afresh_zgxz',
                auth: true,
                method: 'GET',
            },
        },
    },
    proofread:{
        kh:{
            task:{
                url: '/exammanagement/proofread/kh_task',
                auth: true,
                method: 'GET',
            },
            surplus:{
                url: '/exammanagement/proofread/kh_surplus_listpg',
                auth: true,
                method: 'GET',
            },
            all:{
                url: '/exammanagement/proofread/kh_all_listpg',
                auth: true,
                method: 'GET',
            },
            set:{
                url: '/exammanagement/proofread/kh_set',
                auth: true,
                method: 'POST',
            }
        },
        qkwj:{
            task:{
                url: '/exammanagement/proofread/qkwj_task',
                auth: true,
                method: 'GET',
            },
            set:{
                url: '/exammanagement/proofread/qkwj_set',
                auth: true,
                method: 'POST',
            }
        },
        xz:{
            task:{
                url: '/exammanagement/proofread/xz_task',
                auth: true,
                method: 'GET',
            },
            set:{
                url: '/exammanagement/proofread/xz_set',
                auth: true,
                method: 'POST',
            }
        },
        repeat:{
            task:{
                url: '/exammanagement/proofread/repeat',
                auth: true,
                method: 'GET',
            },
            kh_set:{
                url: '/exammanagement/proofread/paper_kh_set',
                auth: true,
                method: 'POST',
            }
        }
    }
}
