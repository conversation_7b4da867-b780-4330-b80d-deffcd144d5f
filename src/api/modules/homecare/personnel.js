export default{
    employee:{
        listByIds:{
            url: '/homecare/base/employee/listByIds',
            auth: true,
            method: 'POST',
        },
        listpg: {
            url: '/homecare/base/employee/listpg',
            auth: true,
            method: 'GET',
        },
        info: {
            url: '/homecare/base/employee/info',
            auth: true,
            method: 'GET',
        },
        save: {
            url: '/homecare/base/employee/save',
            auth: true,
            method: 'POST',
        },
        delete: {
            url: '/homecare/base/employee/delete',
            auth: true,
            method: 'POST',
        },
        contacts:{
            listpg: {
                url: '/homecare/base/employee/contacts_listpg',
                auth: true,
                method: 'GET',
            },
            info: {
                url: '/homecare/base/employee/contacts_info',
                auth: true,
                method: 'GET',
            },
            save: {
                url: '/homecare/base/employee/contacts_save',
                auth: true,
                method: 'POST',
            },
            delete: {
                url: '/homecare/base/employee/contacts_delete',
                auth: true,
                method: 'POST',
            },
        },
        speciality:{
            listpg: {
                url: '/homecare/base/employee/speciality_listpg',
                auth: true,
                method: 'GET',
            },
            info: {
                url: '/homecare/base/employee/speciality_info',
                auth: true,
                method: 'GET',
            },
            save: {
                url: '/homecare/base/employee/speciality_save',
                auth: true,
                method: 'POST',
            },
            delete: {
                url: '/homecare/base/employee/speciality_delete',
                auth: true,
                method: 'POST',
            },
        },
        education:{
            listpg: {
                url: '/homecare/base/employee/education_listpg',
                auth: true,
                method: 'GET',
            },
            info: {
                url: '/homecare/base/employee/education_info',
                auth: true,
                method: 'GET',
            },
            save: {
                url: '/homecare/base/employee/education_save',
                auth: true,
                method: 'POST',
            },
            delete: {
                url: '/homecare/base/employee/education_delete',
                auth: true,
                method: 'POST',
            },
        },
        certificate:{
            listpg: {
                url: '/homecare/base/employee/certificate_listpg',
                auth: true,
                method: 'GET',
            },
            info: {
                url: '/homecare/base/employee/certificate_info',
                auth: true,
                method: 'GET',
            },
            save: {
                url: '/homecare/base/employee/certificate_save',
                auth: true,
                method: 'POST',
            },
            delete: {
                url: '/homecare/base/employee/certificate_delete',
                auth: true,
                method: 'POST',
            },
        },
        work:{
            listpg: {
                url: '/homecare/base/employee/work_listpg',
                auth: true,
                method: 'GET',
            },
            info: {
                url: '/homecare/base/employee/work_info',
                auth: true,
                method: 'GET',
            },
            save: {
                url: '/homecare/base/employee/work_save',
                auth: true,
                method: 'POST',
            },
            delete: {
                url: '/homecare/base/employee/work_delete',
                auth: true,
                method: 'POST',
            },
        },
        signed: {
            dataAllList: {
                url: '/homecare/base/employee/signed_data_all_list',
                auth: true,
                method: 'GET',
            },
            listpg: {
                url: '/homecare/base/employee/signed_listpg',
                auth: true,
                method: 'GET',
            },
            info: {
                url: '/homecare/base/employee/signed_info',
                auth: true,
                method: 'GET',
            },
            infoByEmployee: {
                url: '/homecare/base/employee/signed_infoByEmployee',
                auth: true,
                method: 'GET',
            },
            save: {
                url: '/homecare/base/employee/signed_save',
                auth: true,
                method: 'POST',
            },
            delete: {
                url: '/homecare/base/employee/signed_delete',
                auth: true,
                method: 'POST',
            },
        },
        swipers:{
            listpg: {
                url: '/homecare/base/employee/swipers_listpg',
                auth: true,
                method: 'GET',
            },
            info: {
                url: '/homecare/base/employee/swipers_info',
                auth: true,
                method: 'GET',
            },
            save: {
                url: '/homecare/base/employee/swipers_save',
                auth: true,
                method: 'POST',
            },
            delete: {
                url: '/homecare/base/employee/swipers_delete',
                auth: true,
                method: 'POST',
            },
        },
        content:{
            listpg: {
                url: '/homecare/base/employee/content_listpg',
                auth: true,
                method: 'GET',
            },
            info: {
                url: '/homecare/base/employee/content_info',
                auth: true,
                method: 'GET',
            },
            save: {
                url: '/homecare/base/employee/content_save',
                auth: true,
                method: 'POST',
            },
            delete: {
                url: '/homecare/base/employee/content_delete',
                auth: true,
                method: 'POST',
            },
        }
    }
}
