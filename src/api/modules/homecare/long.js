export default{
    basic:{
        project:{
            list: {
                url: '/homecare/work/project/list',
                auth: true,
                method: 'POST',
            },
            listpg: {
                url: '/homecare/work/project/listpg',
                auth: true,
                method: 'GET',
            },
            info: {
                url: '/homecare/work/project/info',
                auth: true,
                method: 'GET',
            },
            save: {
                url: '/homecare/work/project/save',
                auth: true,
                method: 'POST',
            },
            delete: {
                url: '/homecare/work/project/delete',
                auth: true,
                method: 'POST',
            },
        },
        group:{
            treelist:{
                url: '/homecare/work/group/valid_list',
                auth: true,
                method: 'GET',
            },
            list: {
                url: '/homecare/work/group/list',
                auth: true,
                method: 'POST',
            },
            listpg: {
                url: '/homecare/work/group/listpg',
                auth: true,
                method: 'GET',
            },
            info: {
                url: '/homecare/work/group/info',
                auth: true,
                method: 'GET',
            },
            save: {
                url: '/homecare/work/group/save',
                auth: true,
                method: 'POST',
            },
            delete: {
                url: '/homecare/work/group/delete',
                auth: true,
                method: 'POST',
            },
        },
        groupTeam: {
            list: {
                url: '/homecare/work/groupTeam/list',
                auth: true,
                method: 'POST',
            },
            groupList: {
                url: '/homecare/work/groupTeam/group_list',
                auth: true,
                method: 'GET',
            },
            listpg: {
                url: '/homecare/work/groupTeam/listpg',
                auth: true,
                method: 'GET',
            },
            info: {
                url: '/homecare/work/groupTeam/info',
                auth: true,
                method: 'GET',
            },
            delete: {
                url: '/homecare/work/groupTeam/delete',
                auth: true,
                method: 'POST',
            },
            save: {
                url: '/homecare/work/groupTeam/save',
                auth: true,
                method: 'POST',
            },
            attendant: {
                listpg: {
                    url: '/homecare/work/groupTeam/attendant_all_listpg',
                    auth: true,
                    method: 'GET',
                },
                add: {
                    url: '/homecare/work/groupTeam/attendant_add',
                    auth: true,
                    method: 'POST',
                },
                delete: {
                    url: '/homecare/work/groupTeam/attendant_delete',
                    auth: true,
                    method: 'POST',
                },
                headSet: {
                    url: '/homecare/work/groupTeam/attendant_headSet',
                    auth: true,
                    method: 'POST',
                },
                headCancel: {
                    url: '/homecare/work/groupTeam/attendant_headCancel',
                    auth: true,
                    method: 'POST',
                },
            },
        },
        commData:{
            type:{
                list: {
                    url: '/homecare/work/dataType/list',
                    auth: true,
                    method: 'POST',
                },
                listpg: {
                    url: '/homecare/work/dataType/listpg',
                    auth: true,
                    method: 'GET',
                },
                info: {
                    url: '/homecare/work/dataType/info',
                    auth: true,
                    method: 'GET',
                },
                save: {
                    url: '/homecare/work/dataType/save',
                    auth: true,
                    method: 'POST',
                },
                delete: {
                    url: '/homecare/work/dataType/delete',
                    auth: true,
                    method: 'POST',
                },
                valid:{
                    url: '/homecare/work/dataType/valid_list',
                    auth: true,
                    method: 'GET',
                }
            },
            item:{
                list: {
                    url: '/homecare/work/data/list',
                    auth: true,
                    method: 'POST',
                },
                listpg: {
                    url: '/homecare/work/data/listpg',
                    auth: true,
                    method: 'GET',
                },
                info: {
                    url: '/homecare/work/data/info',
                    auth: true,
                    method: 'GET',
                },
                save: {
                    url: '/homecare/work/data/save',
                    auth: true,
                    method: 'POST',
                },
                delete: {
                    url: '/homecare/work/data/delete',
                    auth: true,
                    method: 'POST',
                },
            },
            all:{
                list:{
                    url: '/homecare/work/data/all_list',
                    auth: true,
                    method: 'GET',
                }
            }
        },
        import:{
            headers: {
                url: '/homecare/base/excel/headers',
                auth: true,
                method: 'GET',
            },
            customer:{
                fields: {
                    url: '/homecare/base/excel/fields_customer',
                    auth: true,
                    method: 'GET',
                },
                import: {
                    url: '/homecare/base/excel/customer_import',
                    auth: true,
                    method: 'POST',
                },
            },
            employee:{
                fields: {
                    url: '/homecare/base/excel/fields_employee',
                    auth: true,
                    method: 'GET',
                },
                import: {
                    url: '/homecare/base/excel/employee_import',
                    auth: true,
                    method: 'POST',
                },
            }
        }
    },
    customer:{
        group:{
            customer:{
                listpg:{
                    url: '/homecare/work/longCustomer/listpg',
                    auth: true,
                    method: 'GET',
                },
                info:{
                    url: '/homecare/work/longCustomer/info',
                    auth: true,
                    method: 'GET',
                },
                listByIds:{
                    url: '/homecare/work/longCustomer/list',
                    auth: true,
                    method: 'POST',
                },
            }
        },
        second:{
            create:{
                url: '/homecare/work/second/createFromCustomer',
                auth: true,
                method: 'POST',

            },
            state:{
                url: '/homecare/work/second/stateListByCustomerIds',
                auth: true,
                method: 'POST',
            }
        },
        project:{
            listpg:{
                url: '/homecare/work/customerProject/listpg',
                auth: true,
                method: 'GET',
            },
            info:{
                url: '/homecare/work/customerProject/info',
                auth: true,
                method: 'GET',
            },
            list:{
                url: '/homecare/work/customerProject/list',
                auth: true,
                method: 'POST',
            },
            add:{
                url: '/homecare/work/customerProject/saveAll',
                auth: true,
                method: 'POST',
            },
            delete:{
                url: '/homecare/work/customerProject/delete',
                auth: true,
                method: 'POST',
            },
            setTime:{
                url:'/homecare/work/customerProject/setTime',
                auth: true,
                method: 'POST',
            },
            setMonthTimes:{
                url:'/homecare/work/customerProject/setMonthTimes',
                auth: true,
                method: 'POST',
            },
            template:{
                save:{
                    url: '/homecare/work/customerProject/template_save',
                    auth: true,
                    method: 'POST',
                },
                delete:{
                    url: '/homecare/work/customerProject/template_delete',
                    auth: true,
                    method: 'POST',
                },
                listpg:{
                    url: '/homecare/work/customerProject/template_listpg',
                    auth: true,
                    method: 'GET',
                },
                info:{
                    url: '/homecare/work/customerProject/template_info',
                    auth: true,
                    method: 'GET',
                },
                times:{
                    save:{
                        url: '/homecare/work/customerProject/template_times_save',
                        auth: true,
                        method: 'POST',
                    },
                    delete:{
                        url: '/homecare/work/customerProject/template_times_delete',
                        auth: true,
                        method: 'POST',
                    },
                    listpg:{
                        url: '/homecare/work/customerProject/template_times_listpg',
                        auth: true,
                        method: 'GET',
                    },
                    info:{
                        url: '/homecare/work/customerProject/template_times_info',
                        auth: true,
                        method: 'GET',
                    }
                }
            }
        },
        location:{
            listpg:{
                url: '/homecare/base/customer/location_change_listpg',
                auth: true,
                method: 'GET',
            },
            audit:{
                url: '/homecare/base/customer/location_change_audit',
                auth: true,
                method: 'POST',
            },
        }
    },
    transaction:{
        salesman:{
            list: {
                url: '/homecare/base/salesman/ist',
                auth: true,
                method: 'GET',
            },
            listpg: {
                url: '/homecare/base/salesman/listpg',
                auth: true,
                method: 'GET',
            },
            info: {
                url: '/homecare/base/salesman/info',
                auth: true,
                method: 'GET',
            },
            save: {
                url: '/homecare/base/salesman/save',
                auth: true,
                method: 'POST',
            },
            delete: {
                url: '/homecare/base/salesman/delete',
                auth: true,
                method: 'POST',
            },
        },
        subscribe:{
            listpg:{
                url: '/homecare/work/person/listpg',
                auth: true,
                method: 'GET',
            },
            listpgNoGovAudit:{
                url: '/homecare/work/person/noGovAudit_listpg',
                auth: true,
                method: 'GET',
            },
            listpgSubmitNoAudit:{
                url: '/homecare/work/person/submit_noGovAudit_listpg',
                auth: true,
                method: 'GET',
            },
            listpgGovPass:{
                url:'/homecare/work/person/govAuditPass_listpg',
                auth: true,
                method: 'GET',
            },
            listpgAudit:{
                url: '/homecare/work/person/submit_govAudit_listpg',
                auth: true,
                method: 'GET',
            },
            info:{
                url: '/homecare/work/person/info',
                auth: true,
                method: 'GET',
            },
            submit:{
                url: '/homecare/work/person/submit',
                auth: true,
                method: 'POST',
            },
            submitCancel:{
                url: '/homecare/work/person/submitCancel',
                auth: true,
                method: 'POST',
            },
            list:{
                url: '/homecare/work/person/list',
                auth: true,
                method: 'POST',
            },
            save:{
                url: '/homecare/work/person/save',
                auth: true,
                method: 'POST',
            },
            delete:{
                url: '/homecare/work/person/delete',
                auth: true,
                method: 'POST',
            },
            dataset:{
                list:{
                    url: '/homecare/work/person/data_allList',
                    auth: true,
                    method: 'GET',
                },
                save:{
                    url: '/homecare/work/person/data_save',
                    auth: true,
                    method: 'POST',
                },
            },
            gov:{
                audit:{
                    url: '/homecare/work/person/gov_audit',
                    auth: true,
                    method: 'POST',
                },
                auditCancel:{
                    url: '/homecare/work/person/gov_auditCancel',
                    auth: true,
                    method: 'POST',
                },
                // auditNoPass:{
                //     url: '/homecare/work/person/gov_auditNoPass',
                //     auth: true,
                //     method: 'POST',
                // }
            },
            distribution:{
                group:{
                    url: '/homecare/work/person/distributionGroup',
                    auth: true,
                    method: 'POST',
                },
                attendant:{
                    url: '/homecare/work/person/distributionAttendant',
                    auth: true,
                    method: 'POST',
                },
            }
        },
        audit:{
            listpg:{
                url: '/homecare/work/person/audit_listpg',
                auth: true,
                method: 'GET',
            },
            info:{
                url: '/homecare/work/person/audit_info',
                auth: true,
                method: 'GET',
            },
        },
        attendant:{
            customer:{
                listpg:{
                    url: '/homecare/base/groupAttendant/customer_location_listpg',
                    auth: true,
                    method: 'GET',
                }
            }
        }
    },
    care:{
        nurse:{
            listByIds: {
                url: '/homecare/base/nurse/list',
                auth: true,
                method: 'POST',
            },
            listpg: {
                url: '/homecare/base/nurse/listpg',
                auth: true,
                method: 'GET',
            },
            info: {
                url: '/homecare/base/nurse/info',
                auth: true,
                method: 'GET',
            },
            save: {
                url: '/homecare/base/nurse/save',
                auth: true,
                method: 'POST',
            },
            delete: {
                url: '/homecare/base/nurse/delete',
                auth: true,
                method: 'POST',
            },
        },
        attendant:{
            list: {
                url: '/homecare/work/groupAttendant/list',
                auth: true,
                method: 'GET',
            },
            listpg: {
                url: '/homecare/work/groupAttendant/listpg',
                auth: true,
                method: 'GET',
            },
            info: {
                url: '/homecare/work/groupAttendant/info',
                auth: true,
                method: 'GET',
            },
            add: {
                url: '/homecare/work/groupAttendant/add',
                auth: true,
                method: 'POST',
            },
            delete: {
                url: '/homecare/work/groupAttendant/delete',
                auth: true,
                method: 'POST',
            },
            receive:{
                listpg:{
                    url: '/homecare/base/groupAttendant/receive_listpg',
                    auth: true,
                    method: 'GET',
                },
                receive:{
                    url: '/homecare/base/groupAttendant/receive',
                    auth: true,
                    method: 'POST',
                }
            }
        },
        head:{
            first:{
                person:{
                    listpg:{
                        url: '/homecare/work/first/person_listpg',
                        auth: true,
                        method: 'GET',
                    },
                    info:{
                        url: '/homecare/work/first/person_info',
                        auth: true,
                        method: 'GET',
                    }
                },
                info:{
                    url: '/homecare/work/first/info',
                    auth: true,
                    method: 'GET',
                },
                save:{
                    url: '/homecare/work/first/save',
                    auth: true,
                    method: 'POST',
                },
                submit:{
                    url: '/homecare/work/first/submit',
                    auth: true,
                    method: 'POST',
                },
                submitCancel:{
                    url: '/homecare/work/first/submitCancel',
                    auth: true,
                    method: 'POST',
                },
                dataset:{
                    list:{
                        url: '/homecare/work/first/data_allList',
                        auth: true,
                        method: 'GET',
                    },
                    save:{
                        url: '/homecare/work/first/data_save',
                        auth: true,
                        method: 'POST',
                    },
                },
                location:{
                    info:{
                        url: '/homecare/work/first/location_info',
                        auth: true,
                        method: 'GET',
                    },
                    save:{
                        url: '/homecare/work/first/location_save',
                        auth: true,
                        method: 'POST',
                    }
                },
                imgHead:{
                    info:{
                        url: '/homecare/work/first/imgHead_info',
                        auth: true,
                        method: 'GET',
                    },
                    save:{
                        url: '/homecare/work/first/imgHead_save',
                        auth: true,
                        method: 'POST',
                    }
                }
            },
            second:{
                listpg:{
                    url: '/homecare/work/second/customer_listpg',
                    auth: true,
                    method: 'GET',
                },
                info:{
                    url: '/homecare/work/second/info',
                    auth: true,
                    method: 'GET',
                },
                save:{
                    url: '/homecare/work/second/save',
                    auth: true,
                    method: 'POST',
                },
                submit:{
                    url: '/homecare/work/second/submit',
                    auth: true,
                    method: 'POST',
                },
                submitCancel:{
                    url: '/homecare/work/second/submitCancel',
                    auth: true,
                    method: 'POST',
                },
                dataset:{
                    list:{
                        url: '/homecare/work/second/data_allList',
                        auth: true,
                        method: 'GET',
                    },
                    save:{
                        url: '/homecare/work/second/data_save',
                        auth: true,
                        method: 'POST',
                    },
                },
            },
            audit:{
                first:{
                    listpg:{
                        url: '/homecare/work/first/person_listpg',
                        auth: true,
                        method: 'GET',
                    },
                    info:{
                        url: '/homecare/work/first/person_info',
                        auth: true,
                        method: 'GET',
                    },
                    audit:{
                        url: '/homecare/work/first/audit',
                        auth: true,
                        method: 'POST',
                    },
                },
                second:{
                    listpg:{
                        url: '/homecare/work/second/customer_listpg',
                        auth: true,
                        method: 'GET',
                    },
                    info:{
                        url: '/homecare/work/second/info',
                        auth: true,
                        method: 'GET',
                    },
                    audit:{
                        url: '/homecare/work/second/audit',
                        auth: true,
                        method: 'POST',
                    },
                }
            },
            distribution:{
                listpg:{
                    url: '/homecare/work/distribution/listpg',
                    auth: true,
                    method: 'GET',
                }
            },
            scheduling:{
                customer:{
                    listpg:{
                        url: '/homecare/base/groupAttendant/customer_scheduling_listpg',
                        auth: true,
                        method: 'GET',
                    },
                    dateListpg:{
                        url: '/homecare/work/customerScheduling/listpg',
                        auth: true,
                        method: 'GET',
                    },
                    dateInfo:{
                        url: '/homecare/work/customerScheduling/info',
                        auth: true,
                        method: 'GET',
                    },
                    dateDelete:{
                        url: '/homecare/work/customerScheduling/delete',
                        auth: true,
                        method: 'POST',
                    },
                    dateSave:{
                        url: '/homecare/work/customerScheduling/save',
                        auth: true,
                        method: 'POST',
                    },
                    project:{
                        listpg:{
                            url: '/homecare/work/customerScheduling/project_listpg',
                            auth: true,
                            method: 'GET',
                        },
                        info:{
                            url: '/homecare/work/customerScheduling/project_info',
                            auth: true,
                            method: 'GET',
                        },
                        delete:{
                            url: '/homecare/work/customerScheduling/project_delete',
                            auth: true,
                            method: 'POST',
                        },
                        addMore:{
                            url: '/homecare/work/customerScheduling/project_addMore',
                            auth: true,
                            method: 'POST',
                        }
                    },
                    quick:{
                        projectList:{
                            url: '/homecare/work/customerScheduling/quick_project_list',
                            auth: true,
                            method: 'GET',
                        },
                        build:{
                            url: '/homecare/work/customerScheduling/quickBuild',
                            auth: true,
                            method: 'POST',
                        },
                        save:{
                            url: '/homecare/work/customerScheduling/quickBuild_save',
                            auth: true,
                            method: 'POST',
                        },
                    },
                    report:{
                        mxListpg:{
                            url: '/homecare/work/customerScheduling/schduling_mx_listpg',
                            auth: true,
                            method: 'GET',
                        },
                    },
                    change:{
                        listpg:{
                            url: '/homecare/work/customerScheduling/change_listpg',
                            auth: true,
                            method: 'GET',
                        },
                        info:{
                            url: '/homecare/work/customerScheduling/change_info',
                            auth: true,
                            method: 'GET',
                        },
                        audit:{
                            url: '/homecare/work/customerScheduling/change_audit',
                            auth: true,
                            method: 'POST',
                        },
                    },
                },
                project:{
                    classify:{
                        set:{
                            url: '/homecare/work/project/setBigType',
                            auth: true,
                            method: 'POST',
                        },
                    },
                    repel:{
                        listpg:{
                            url: '/homecare/work/project/allRepel_listpg',
                            auth: true,
                            method: 'GET',
                        },
                        add:{
                            url: '/homecare/work/project/repel_add',
                            auth: true,
                            method: 'POST',
                        },
                        delete:{
                            url: '/homecare/work/project/repel_delete',
                            auth: true,
                            method: 'POST',
                        },
                        repelListpg:{
                            url: '/homecare/work/project/repel_listpg',
                            auth: true,
                            method: 'GET',
                        },
                    },
                    splitDays:{
                        set:{
                            url: '/homecare/work/project/setSplitDays',
                            auth: true,
                            method: 'POST',
                        }
                    }
                },
                template:{
                    list:{
                        url:'/homecare/work/scheduling/template_list',
                        auth: true,
                        method: 'GET',
                    },
                    save:{
                        url:'/homecare/work/scheduling/template_save',
                        auth: true,
                        method: 'POST',
                    },
                    build:{
                        url:'/homecare/work/scheduling/template_build',
                        auth: true,
                        method: 'POST',
                    },
                    buildSave:{
                        url:'/homecare/work/scheduling/build_save',
                        auth: true,
                        method: 'POST',
                    }
                },
            },
        },
        sign:{
            list: {
                url: '/homecare/work/checkInSet/list',
                auth: true,
                method: 'GET',
            },
            listpg: {
                url: '/homecare/work/checkInSet/listpg',
                auth: true,
                method: 'GET',
            },
            info: {
                url: '/homecare/work/checkInSet/info',
                auth: true,
                method: 'GET',
            },
        },
        work:{
            listpg: {
                url: '/homecare/work/work/listpg',
                auth: true,
                method: 'GET',
            },
            info: {
                url: '/homecare/work/work/info',
                auth: true,
                method: 'GET',
            },
            save: {
                url: '/homecare/work/work/save',
                auth: true,
                method: 'POST',
            },
            delete: {
                url: '/homecare/work/work/delete',
                auth: true,
                method: 'POST',
            },
            tjListpg: {
                url: '/homecare/work/work/tj_listpg',
                auth: true,
                method: 'GET',
            },
            tjInfo: {
                url: '/homecare/work/work/tj_info',
                auth: true,
                method: 'GET',
            },
            submit:{
                url: '/homecare/work/work/submit',
                auth: true,
                method: 'POST',
            },
            submitCancel:{
                url: '/homecare/work/work/submitCancel',
                auth: true,
                method: 'POST',
            },
            audit:{
                url: '/homecare/work/work/audit',
                auth: true,
                method: 'POST',
            },
            auditCancel:{
                url: '/homecare/work/work/auditCancel',
                auth: true,
                method: 'POST',
            },
            saveAll:{
                url: '/homecare/work/work/save_all',
                auth: true,
                method: 'POST',
            },
            groupCustomerInfo:{
                url: '/homecare/base/groupCustomer/info',
                auth: true,
                method: 'GET',
            },
            groupCustomerListpg:{
                url: '/homecare/base/groupCustomer/listpg',
                auth: true,
                method: 'GET',
            },
            workAddInfo:{
                url: '/homecare/work/work/add_info',
                auth: true,
                method: 'GET',
            },
            dataset:{
                struct:{
                    url: '/homecare/work/work/data_structList',
                    auth: true,
                    method: 'GET',
                },
            },
            project:{
                addBatch: {
                    url: '/homecare/work/work/project_addBatch',
                    auth: true,
                    method: 'POST',
                },
                allListpg: {
                    url: '/homecare/work/work/allProject_listpg',
                    auth: true,
                    method: 'GET',
                },
                customerAllLispg: {
                    url: '/homecare/work/work/customer_project_listpg',
                    auth: true,
                    method: 'GET',
                },
                listpg: {
                    url: '/homecare/work/work/project_listpg',
                    auth: true,
                    method: 'GET',
                },
                delete: {
                    url: '/homecare/work/work/project_delete',
                    auth: true,
                    method: 'POST',
                },
            },
            error:{
                listpg:{
                    url: '/homecare/work/work/error_listpg',
                    auth: true,
                    method: 'GET',
                },
                info:{
                    url: '/homecare/work/work/error_info',
                    auth: true,
                    method: 'GET',
                },
                audit:{
                    url: '/homecare/work/work/error_audit',
                    auth: true,
                    method: 'POST',
                },
            },
            proofread: {
                listpg: {
                    url: '/homecare/work/work/proofread_listpg',
                    auth: true,
                    method: 'GET',
                },
                task: {
                    url: '/homecare/work/work/proofread_task',
                    auth: true,
                    method: 'GET',
                },
                save: {
                    url: '/homecare/work/work/proofread_save',
                    auth: true,
                    method: 'POST',
                },
                saveTask: {
                    url: '/homecare/work/work/proofread_save_task',
                    auth: true,
                    method: 'POST',
                },
                error: {
                    listpg: {
                        url: '/homecare/work/work/proofread_error_listpg',
                        auth: true,
                        method: 'GET',
                    },
                    save: {
                        url: '/homecare/work/work/proofread_error_save',
                        auth: true,
                        method: 'POST',
                    },
                }
            },
            judan: {
               save: {
                   url: '/homecare/work/work/judan',
                   auth: true,
                   method: 'POST',
               },
               cancel: {
                   url: '/homecare/work/work/judan_cancel',
                   auth: true,
                   method: 'POST',
               },
            },
        },
        plan:{
            setting:{
                template:{
                    attendantListpg:{
                        url: '/homecare/work/template/attendant_listpg',
                        auth: true,
                        method: 'GET',
                    },
                    attendantInfo:{
                        url: '/homecare/work/template/attendant_template_info',
                        auth: true,
                        method: 'GET',
                    },
                    attendantSave:{
                        url: '/homecare/work/template/attendant_template_save',
                        auth: true,
                        method: 'POST',
                    },
                    attendantBuild:{
                        url: '/homecare/work/template/attendant_template_build',
                        auth: true,
                        method: 'POST',
                    },
                    attendantDelete:{
                        url: '/homecare/work/template/attendant_template_delete',
                        auth: true,
                        method: 'POST',
                    },
                    attendantCustomerDelete:{
                        url: '/homecare/work/template/attendant_template_customer_delete',
                        auth: true,
                        method: 'POST',
                    }
                }
            }
        },
        scheduling:{
            tree:{
                url: '/homecare/base/groupAttendant/tree_list',
                auth: true,
                method: 'GET',
            },
            list:{
                url: '/homecare/work/customerScheduling/date_list',
                auth: true,
                method: 'GET',
            },
            build:{
                url: '/homecare/work/customerScheduling/attendant_template_build',
                auth: true,
                method: 'POST',
            },
            delete:{
                url: '/homecare/work/customerScheduling/attendant_delete',
                auth: true,
                method: 'POST',
            },
            lock:{
                url: '/homecare/work/customerScheduling/customer_lock',
                auth: true,
                method: 'POST',
            },
            unlock:{
                url: '/homecare/work/customerScheduling/customer_unlock',
                auth: true,
                method: 'POST',
            },
            audit:{
                url: '/homecare/work/customerScheduling/customer_audit',
                auth: true,
                method: 'POST',
            },
            auditCancel:{
                url: '/homecare/work/customerScheduling/customer_auditCancel',
                auth: true,
                method: 'POST',
            },
            //保存
            save:{
                url: '/homecare/work/customerScheduling/save',
                auth: true,
                method: 'POST',
            },
            //删除
            schedulingDelete:{
                url: '/homecare/work/customerScheduling/delete',
                auth: true,
                method: 'POST',
            },
            info:{
                url: '/homecare/work/customerScheduling/info',
                auth: true,
                method: 'GET',
            },
            customerScheduling:{
                url: '/homecare/work/customerScheduling/customer_template_build',
                auth: true,
                method: 'POST',
            },
            customerSchedulingDelete:{
                url: '/homecare/work/customerScheduling/customer_delete',
                auth: true,
                method: 'POST',
            },
            customerWait:{
                url: '/homecare/work/customerScheduling/customer_wait',
                auth: true,
                method: 'POST',
            },
            customerWaitCancel:{
                url: '/homecare/work/customerScheduling/customer_waitCancel',
                auth: true,
                method: 'POST',
            },
            project:{
                listpgByDate:{
                    url: '/homecare/work/customerScheduling/date_project_listpg',
                    auth: true,
                    method: 'GET',
                },
                listpg:{
                    url: '/homecare/work/customerScheduling/project_listpg',
                    auth: true,
                    method: 'GET',
                },
                suggestion:{
                    url: '/homecare/work/customerScheduling/suggestion_project_build',
                    auth: true,
                    method: 'GET',
                },
                add:{
                    url: '/homecare/work/customerScheduling/project_addMore',
                    auth: true,
                    method: 'POST',
                },
                delete:{
                    url: '/homecare/work/customerScheduling/project_delete',
                    auth: true,
                    method: 'POST',
                },
            },
            customer:{
                dateListpg:{
                    url: '/homecare/work/customerScheduling/date_listpg',
                    auth: true,
                    method: 'POST',
                }
            }
        }

    },

}
