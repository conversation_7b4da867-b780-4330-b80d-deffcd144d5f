<template>
    <div class="search-box">
        <!-- logo -->
        <div class="logo-area">
            <img class="logo" src="@/assets/ut100.png">
        </div>
        <!-- 搜索框 -->
        <div class="search-list">
            <div class="search">
                <input
v-model="inputValue" :class="showRecommend == true ? 'search-input-active' : 'search-input'"
                    placeholder="感受教学魅力" @click="showRecommend = true" />
                <el-button type="primary" class="search-btn" @click="search"><i
                        class="el-icon-search search-icon"></i></el-button>

                        <div v-if="showRecommend == true" class="search-active">
                <div class="recommend">
                    <div>为你推荐</div>
                    <div @click="cancel"><i class="el-icon-close" style="font-weight: bold;"></i></div>
                </div>
                <div class="rec-title">
                    <template v-for="(item, index) in recommendList">
                        <div :key="index" class="rec-list">
                            <i v-if="item.active == false" class="el-icon-view"></i>
                            <i v-if="item.active == true" class="el-icon-view" style="color:red;"></i>
                            <span style="margin-left: 4px;color:gray;">{{ item.name }}</span>
                        </div>
                    </template>
                </div>
                <!-- <div class="upload-img">
                    <el-upload class="upload" action="https://jsonplaceholder.typicode.com/posts/" drag multiple>
                        <i class="el-icon-upload"></i>
                        <div class="el-upload__text">将文件拖到此处，或<em>点击上传</em></div>
                    </el-upload>
                </div> -->
            </div>
            </div>
            
        </div>
        <!-- app下载 -->
        <div class="func-list">
            <el-tooltip content="APP" placement="bottom" effect="light" :visible-arrow="false">
                <div class="app-download">
                    <div class="app-item">
                        <i class="el-icon-download icon"></i>
                    </div>
                    <span style="margin-left: 5px;color: #1890FF;">APP</span>
                </div>
            </el-tooltip>
            <el-tooltip content="阅卷" placement="bottom" effect="light" :visible-arrow="false">
                <div class="app-download">
                    <div class="app-item">
                        <template v-for="(item, index) in animList">
                            <span :key="index" class="icon" :style="{ 'animation-duration': (index + 1) * 2 + 's' }">{{
                                item.img
                            }}</span>
                        </template>
                    </div>
                    <span style="margin-left: 5px;color: #1890FF;">阅卷</span>
                </div>
            </el-tooltip>
        </div>
    </div>
</template>
<script>
export default {
    data() {
        return {
            showRecommend: false,
            // focusState: false,
            inputValue: '',
            animList: [
                {
                    id: '1',
                    img: 'e'
                },
            ],
            recommendList: [
                {
                    id: '1',
                    active: true,
                    name: '期末真题速提'
                },
                {
                    id: '2',
                    active: true,
                    name: '计算能力培养'
                },
                {
                    id: '3',
                    active: true,
                    name: '“双减”作业方案'
                },
                {
                    id: '4',
                    active: true,
                    name: '二级能力高效解题'
                },
                {
                    id: '5',
                    active: false,
                    name: '2023年高考指向'
                }
            ]
        }
    },
    methods: {
        cancel() {
            this.showRecommend = false
        },
        search() {
            this.$notify({
                title: '搜索中',
                message: '等一哈！',
                type: 'warning'
            })
        }
    }
}
</script>
<style lang="scss" scoped>
.logo-area {
    font-size: 40px;
    width: 20%;
    height: 100px;
    color: #1890FF;
    display: flex;
    align-items: center;
}

.logo{
    width:100%;
}

.search-list {
    width: 60%;
    display: flex;
    align-items: center;
    position: relative;
    justify-content: center;

    .search {
        border-radius: 6px;
        height: 44px;
        display: flex;
        align-items: center;
        line-height: 44px;
        // border: 2px solid #1890FF;

        .search-input {
            width: 450px;
            padding: 0px 20px;
            height: 44px;
            border-radius: 6px 0px 0px 6px;
            border: 2px solid #1890FF;
        }

        .search-input-active {
            width: 450px;
            padding: 0px 20px;
            height: 44px;
            border-top: 2px solid #1890FF;
            border-left: 2px solid #1890FF;
            border-right: 2px solid #1890FF;
            border-bottom: 3px solid rgba(0, 0, 0, 0.1);
            border-radius: 6px 0px 0px 0px;
        }

        .search-input::-webkit-input-placeholder {
            color: gray;
        }

        .search-input::-moz-placeholder {
            color: gray;
        }

        .search-input:-moz-placeholder {
            color: gray;
        }

        .search-input:-ms-input-placeholder {
            color: gray;
        }

        .search-btn {
            border-radius: 0 6px 6px 0;
            height: 44px;
            width: 60px;

            .search-icon {
                font-size: 18px;
                font-weight: bold;
            }
        }
    }

    .search-active {
        position: absolute;
        top: 70px;
        border: 2px solid #1890FF;
        border-top: none;
        border-radius: 0px 0px 6px 6px;
        width: 450px;
        height: 280px;
        z-index: 8;
        background-color: #fff;

        .recommend {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 0px 10px;
            margin: 6px 0px 0px;
            font-size: 16px;
            color: #999999;
        }

        .rec-title {
            display: flex;
            flex-wrap: wrap;
            max-height: 110px;
            overflow: hidden;

            .rec-list {
                display: flex;
                cursor: pointer;
                display: flex;
                align-items: center;
                padding: 4px 8px;
                margin: 6px 10px;
                border-radius: 50px;
                background-color: #F8F8F8;
            }
        }

        .upload-img {
            padding: 20px;
            height: 120px;
        }
    }
}

.func-list {
    display: flex;
    justify-content: space-between;
    align-items: center;
    width: 20%;

    .app-download {
        display: flex;
        align-items: center;

        .app-item {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            border: 2px solid #1890FF;
            position: relative;

            .icon {
                color: #1890FF;
                font-size: 28px;
                position: absolute;
                left: 50%;
                top: 50%;
                transform: translate(-50%, -50%);
            }

            .anim-icon {
                animation: pic ease 0s 1 normal none;
            }

            @keyframes pic {
                0% {
                    transform: translateX(100px);
                    opacity: 0;
                }

                100% {
                    transform: translateX(0);
                    opacity: 1;
                }
            }
        }
    }

    .app-download:hover {
        cursor: pointer;

        .app-item {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            border: 2px solid #1890FF;
            position: relative;
            background-color: #1890FF;

            .icon {
                color: #fff;
                font-size: 28px;
                position: absolute;
                left: 50%;
                top: 50%;
                transform: translate(-50%, -50%);
            }
        }
    }
}

.search-box {
    background-color: #fff;
    display: flex;
    justify-content: space-between;
    width:100%;

}
</style>
