<template>

<VueDragResize
    v-if="showPanel"
    :is-draggable="true"
    :is-resizable="false"
    :parent-limitation="true"
    :x="left" :y="top" w="auto" h="auto" :z="1006"
    :is-active="false"
    style="outline:none;"
    drag-handle=".keyboard .drag,.keyboard .drag i"
    @resizing="resize"
    @dragging="resize"
    >
    <div class="keyboard en-noselect" @click="panelClick()">
        <div class="pull-left drag">
            <i class="en-icon el-icon-more"></i>
        </div>
        <div class="score-tool">
            <div class="item">
                <i class="el-icon-question" />问题卷
            </div>
            <div class="item" @click="scoreFullClick()">
                <i class="el-icon-trophy" />满分
            </div>
            <div class="item" @click="scoreZeroClick()">
                <i class="el-icon-news" />零分
            </div>
        </div>
        <div class="content">
            <div class="markings">
                <template v-for="(item,index) in sub">
                    <div :key="'subject'+index" class="subject" :class="currIndex==index?'currRow':''" @click="$refs.input[index].focus()" >
                        <div class="title pull-left">{{item.title}}</div>
                        <div class="score pull-left">
                            <input
                                ref="input"
                                v-model="item.scoreMy"
                                :placeholder="'满分'+item.scoreTotal+'分'"
                                class="score-input"
                                :class="item.scoreMy>item.scoreTotal?'error':''"
                                :max-length="5"
                                @focus="inputFocus(index)"
                                @keyup="item.scoreMy = checkInput(item.scoreMy)"
                                @keydown="onkeydown"
                                @input="oninput"
                                />
                            
                            <span v-if="item.scoreMy>item.scoreTotal" style="color:#ff0000">超出</span>
                        </div>
                        <div class="op pull-left">
                            <div class="item" @click="item.scoreMy =item.scoreTotal">
                                <i class="el-icon-check"/>
                            </div>
                            <div class="item" @click="item.scoreMy ='0'">
                                <i class="el-icon-close" />
                            </div>
                        </div>
                    </div>
                </template>
                <div class="sum">总得分:<span class="score"> {{getScoreMySum()}} </span>分</div>
            </div>
        </div>

        <div class="submit">
            <div class="btn-box">
                <button v-if="getSubmitBtnState() && imageLoaded" @click="$emit('submit')"><i class="el-icon-document-checked" />提交</button>
                <button v-else disabled><i class="el-icon-document-checked" />提交</button>
            </div>
            <div class="paper-op my-checked">
                <div class="collection-btn" :class="{ collected: collection }" @click="toggleCollection">
                    <i :class="collection ? 'el-icon-star-on' : 'el-icon-star-off'"></i>
                    <span>{{ collection ? '已收藏' : '收藏' }}</span>
                </div>
            </div>
        </div>

    </div>
</VueDragResize>

</template>

<script>
import VueDragResize from '@/extra/vue-drag-resize'
import {decimalsAdd} from './operation'


export default {
    components: {
            VueDragResize
    },
    directives: {
        focus: {
            inserted: function (el) {
                el.focus()
            }
        }
    },
    props:{
        showPanel:{
            type:Boolean,
            default:true
        },
        showCollection:{
           type:Boolean,
            default:false
        },
        subMarking:{
            type:Array,
            default:() => {
                return []
            }
        },
        oneKey:{
            type:Boolean,
            default:false,
        },
        markingIndex:{
            type:Number,
            default:-1
        },
        imageLoaded:{
            type:Boolean,
            default:false
        }
    },
    data(){
        return{
            width: 0,
            height: 0,
            top: 0,
            left: 0,
            sub:[],
            currIndex:-1,
            imgLoad:false,
            collection:false,
        }
    },
    watch:{
        currIndex(newValue,oldValue){
            if(newValue!=oldValue && newValue>=0)
            {
                this.$refs.input[newValue].focus()
                this.$refs.input[newValue].select()
                this.$emit("markingIndexChange", newValue);
            }
        },
        markingIndex(newValue,oldValue){
             if(newValue!=oldValue && newValue>=0)
            {
                this.currIndex=newValue
            }
        },
        imageLoaded(newValue){
            this.imgLoad=newValue
        },
        subMarking(newValue){
            this.sub=newValue
            this.$nextTick(()=>{
                this.$refs.input[0].focus()
                this.$refs.input[0].select()
            })
        }
    },
    created(){
        let width=parseInt(document.body.clientWidth) - 350
        let _left=sessionStorage.getItem("keyboard-left") || width
        let _top=sessionStorage.getItem("keyboard-top") || 55
        _left=parseInt(_left)
        _top=parseInt(_top)
        this.left = _left
        this.top = _top
        this.sub=this.subMarking
        this.currIndex=this.markingIndex
       // console.log(this.subMarking,this.$refs.input)
    },

    methods:{
        resize(newRect) {
            this.top = newRect.top;
            this.left = newRect.left;
            sessionStorage.setItem("keyboard-left",this.left)
            sessionStorage.setItem("keyboard-top",this.top)
        },
        getScoreMySum(){
            let sum=0
            for(let i=0;i<this.sub.length;i++){
                let mySocreItem=this.sub[i].scoreMy
                if(mySocreItem){
                    sum=decimalsAdd(sum,parseFloat(mySocreItem))
                }
            }
            return sum
        },
        getSubScore(){
            let arr=[]
            for(let i=0;i<this.sub.length;i++){
                let mySocreItem=this.sub[i]
                arr.push({
                    id:mySocreItem.id,
                    score:mySocreItem.scoreMy
                })
            }
            return arr
        },
        oninput(){
            this.$emit("scoreMyChange",this.sub)
        },
        collectionChange(checked){
            this.$emit("collectionChange", checked);
        },
        toggleCollection(){
            this.collection = !this.collection;
            this.collectionChange(this.collection);
        },
        inputFocus(i){
           this.currIndex = i
        },
        panelClick(){
            if(this.currIndex>=0){
                this.$refs.input[this.currIndex].focus()
                this.$refs.input[this.currIndex].select()
            }
        },
        scoreFullClick(){
            for(let i=0;i<this.sub.length;i++){
                this.sub[i].scoreMy=this.sub[i].scoreTotal
            }
            this.$emit("scoreMyChange",this.sub)
            if(this.getSubmitBtnState() && this.oneKey){ this.$emit("submit")}
        },
        scoreZeroClick(){
            for(let i=0;i<this.sub.length;i++){
                this.sub[i].scoreMy='0'
            }
            this.$emit("scoreMyChange",this.sub)
            if(this.getSubmitBtnState() && this.oneKey){ this.$emit("submit")}
        },
        getSubmitBtnState(){
            for(let i=0;i<this.sub.length;i++){
                if(this.sub[i].scoreMy=='' || this.sub[i].scoreMy==null || this.sub[i].scoreMy==undefined){
                    return false
                }
                else if( parseFloat(this.sub[i].scoreMy)>this.sub[i].scoreTotal) return false
            }
            return true
        },
        onkeydown(e){
            if(e.code=='ArrowUp' && this.currIndex>0) this.currIndex--
            if(e.code=='ArrowDown' && this.currIndex<this.sub.length-1) this.currIndex++
            if((e.code=="Enter" || e.code=="NumpadEnter") && e.target.value!=''){
                if(this.currIndex<this.sub.length-1){
                    this.currIndex++
                }
                else {
                    for(let i=0;i<this.sub.length;i++){
                        if(this.sub[i].scoreMy=='' || this.sub[i].scoreMy==null || this.sub[i].scoreMy==undefined){
                            this.currIndex=i
                            break;
                        }
                    }
                    if(this.getSubmitBtnState() && this.oneKey){ this.$emit("submit")}
                    return
                }
            }
        },
        checkInput(num) {
            let str = num.toString()
            var len1 = str.substr(0, 1)
            var len2 = str.substr(1, 1)
            //如果第一位是0，第二位不是点，就用数字把点替换掉
            if (str.length > 1 && len1 == 0 && len2 != ".") {
                str = str.substr(1, 1);
            }
            //第一位不能是.
            if (len1 == ".") {
                str = "";
            }
            //限制只能输入一个小数点
            if (str.indexOf(".") != -1) {
                var str_ = str.substr(str.indexOf(".") + 1);
                if (str_.indexOf(".") != -1) {
                    str = str.substr(0, str.indexOf(".") + str_.indexOf(".") + 1)
                }
                if (str_.length > 2) {
                    // this.$message.warning(`金额小数点后只能输入两位，请正确输入！`)
                    return (str = "")
                }
            }
            //正则替换
            str = str.replace(/[^\d^.]+/g, '') // 保留数字和小数点
            return str
        },

    },

}
</script>

<style lang="scss" scoped>
    .keyboard{
        z-index: 100;
        min-width:300px;
        height: auto;
        background: #2f4050;
        color: #ccc;
        border-radius: 5px;
        box-shadow:0 0 5px #888;
        padding: 8px 5px 0 5px;
        overflow: hidden;
        transition: all .2s ease-in 0ms;
    }

    .drag{
        float: left;
        padding: 8px 0;
        cursor: move;
        color: orange;
        text-align: center;
        width:20px;
        transform: rotate(90deg);

        i{
            padding: 0 !important;
        }
    }

    .score-tool .item{
        font-size: 13px;
        padding: 2px 8px;
        margin-left: 14px;
        border: 1px solid rgb(88, 88, 88);
        border-radius: 5px;
        margin-top: 2px;
        color:#888
    }

    .score-tool .item:first-child {
         margin-left: 4px;
    }


    .keyboard .item{
        float:left;
    }

    .keyboard i{
        font-size: 1rem;
        padding:4px;
        font-weight: bold;
    }

    .keyboard .item:hover{
         background: #4b5864;
         cursor: pointer;
        color:#eee;
        border-color: #eee;
        transition:  0.3s;
    }

    .keyboard .content{
        clear: both;
        margin-top: 5px;
        padding: 7px 5px;
    }

    .keyboard .content .markings{
        float: left;
        width:100%;
        padding: 8px 4px 8px 4px;
        background: #374755;
        border-radius: 2px;
        min-height: 50px
    }

    .score-input{
        font-size: 21px !important;
        font-weight: bold !important;
        background: #949494ab !important;
        border: #949494 !important;
        padding: 0 5px !important;
        color:#fefefe !important;
        height: 28px !important;
        line-height: 1;
        width:60px;
        text-align: center;
        border-radius: 2px;
    }
    .score-input:focus{
        background: #fff !important;
        border: #efefef !important;
        color:#000 !important;
    }
    .score-input::-webkit-input-placeholder{/* WebKit browsers */
        font-weight:normal;
        font-size: 12px;
        padding: 0 2px !important;
    }
    .score-input::-moz-placeholder { /* Mozilla Firefox 19+ */
        font-weight:normal;
        font-size: 12px;
        padding: 0 2px !important;
    }
    .score-input:-ms-input-placeholder { /* Internet Explorer 10+ */
        font-weight:normal;
        font-size: 12px;
        padding: 0 2px !important;
    }

    .currRow .score-input{
        background: #fff !important;
        border: #efefef !important;
        color:#000 !important;
    }

    .error,.currRow .error,.score-input.error:focus{
        background: orangered !important;
    }
    .score-input.error{
        width: 48px;
    }


    .markings .subject{
        padding-top: 5px;
        padding-bottom: 5px;
        height: 40px;
        border-bottom: 1px dashed #888;

        .pull-left{
            float: left;
        }
    }
    .markings .subject:last-child{
        border-bottom: 0;
    }
    .markings .title{
        line-height: 28px;
        width:140px;
        padding: 0 5px;
        font-weight: bold;
        font-size: 110%;
    }
    .markings .score{
        width:70px;
        height: 28px;
        overflow:hidden;
    }

    .markings .op{
        padding-top: 0;
    }

    .markings .op .item{
        padding: 0 2px;
        border: 1px solid rgb(88, 88, 88);
        border-radius: 4px;
        margin-top: 2px;
        color:#888
    }

     .markings .op .item:hover{
         background: #4b5864;
         cursor: pointer;
        color:#ddd;
        border-color: #ddd;
        transition:  0.3s;
    }

    .markings .sum{
        padding-top: 8px;
        float: left;
    }
    .markings .sum .score{
        font-size:18px;
        color: orange;
    }


    .submit{
        float: left;
        width:100%;
        line-height: 32px;
        padding: 7px 5px;
    }
    .submit .btn-box{
        float: right;

        button{
            border: 1px solid #1890ff;
            background-color: #1890ff;
            border-radius: 5px;
            color:#fff
        }
        button:disabled{
            background: #2f4050;
            border: 1px solid rgb(88, 88, 88);
            color:#888
        }
    }



    .keyboard .paper-op{
        float: left;
        width:10rem;
        padding: 5px 5px 0 5px;
        display: flex;
        justify-content: flex-start;
        flex-wrap: nowrap;
        flex-direction: row;
    }

    .my-checked button{
        margin-right: 12px;
    }

    .collection-btn{
        padding-right: 10px;
        display: flex;
        align-items: center;
        cursor: pointer;
        color: #888;
        font-size: 12px;
        user-select: none;
        transition: all 0.4s ease-out;

        i, span {
            transition: all 0.4s ease-out;
        }
    }

    .collection-btn:hover {
        color: #eee;
    }

    .collection-btn:not(.collected) i {
        animation: starFadeIn 0.3s ease-out;
    }

    .collection-btn.collected {
        color: rgb(255, 165, 0);
        animation: collectGlow 0.3s ease-out;
    }

    .collection-btn.collected:hover {
        color: rgb(255, 165, 0);
    }

    .collection-btn:active {
        i {
            transform: scale(0.8);
        }
        span {
            transform: translateX(-4px);
        }
    }

    .collection-btn i {
        font-size: 16px;
        margin-right: 4px;
        padding: 0;
        transform-origin: center center;
    }

    .collection-btn.collected i {
        animation: starBounce 0.6s ease-out;
        text-shadow: 0 0 8px rgba(255, 165, 0, 0.6);
    }

    @keyframes collectGlow {
        0% {
            color: #888;
            text-shadow: none;
        }
        50% {
            color: rgb(255, 165, 0);
            text-shadow: 0 0 12px rgba(255, 165, 0, 0.8);
        }
        100% {
            color: rgb(255, 165, 0);
            text-shadow: 0 0 8px rgba(255, 165, 0, 0.6);
        }
    }
    @keyframes starBounce {
        0% {
            transform: scale(0.8);
        }
        30% {
            transform: scale(1.4) translateY(-3px);
        }
        60% {
            transform: scale(1.1) translateY(1px);
        }
        100% {
            transform: scale(1) rotate(0deg);
        }
    }
    @keyframes starFadeIn {
        0% {
            opacity: 0.5;
            transform: scale(0.8);
        }
        100% {
            opacity: 1;
            transform: scale(1);
        }
    }
    @keyframes textFadeIn {
        0% {
            opacity: 0.5;
        }
        100% {
            opacity: 1;
        }
    }

    .collection-btn span{
        white-space: nowrap;
    }


     .keyboard .line{
        margin-top: 5px;
        width: 100%;
        height: 5px;
        border-top: 1px solid #4b5864;
     }


     :deep(){
    .ri-question-line{
        font-size: 22px;
    }
}

.vdr.active:before {
    outline: none;
}
</style>
