<template>
	<div class="KSIMG-box">
		<div v-if="showLoading" class="loading"></div>
		<div ref="KSIMG" class="KSIMG" :class="[getBoxCSS(), { 'annotations-disabled': !showAnnotations }]">
			<template v-for="(item,index) in url">
				<my-img v-show="!showLoading" :key="index" :src="item" :width="defaultWidth" @load="load" />
			</template>
			
			<my-box
				v-for="(item, index) in rect"
				:key="'rect' + index"
				:rect="rect[index]"
				:show-border="subMarking[index].border"
				:index="index"
				:tool="tool"
				:paper-scale="paperScale"
				:page-scale="pageScale"
				:show-annotations="showAnnotations"
				:class="currIndex == index ? 'selected' : ''"
				@indexChange="indexChange"
				@boxMouseDown="boxMouseDown"
				@addText="addText"
			/>
			<my-flag
				v-for="(item, index) in flag"
				v-show="showAnnotations"
				:key="'flag' + index"
				:tool="tool"
				:marking-index="item.index"
				:flag-index="index"
				:flag="flag[index]"
				:paper-scale="paperScale"
				:page-scale="pageScale"
				:show-annotations="showAnnotations"
				@flagClick="flagClick"
			/>
		</div>
	</div>
</template>

<script>
	import MyImg from './image'
	import MyBox from './box.vue'
	import MyFlag from './flag.vue'

	import { accDiv } from './operation'

	export default {
		components: {
			MyImg,
			MyBox,
			MyFlag,
		},
		props: {
			markingIndex: {
				type: Number,
				default: -1,
			},
			tool: {
				type: String,
				default: '',
			},
			stepScore: {
				type: Number,
				default: 1,
			},
			calcModel: {
				type: Boolean,
				default: false,
			},
			pageScale: {
				type: Number,
				default: 1,
			},
			paperScale: {
				type: Number,
				default: 1,
			},
			rect: {
				type: Array,
				default: function () {
					return []
				},
			},
			url: {
				type: Array,
				default: function () {
					return []
				},
			},
			flag: {
				type: Array,
				default: function () {
					return []
				},
			},
			full: {
				type: Boolean,
				default: false,
			},
			showAnnotations: {
				type: Boolean,
				default: true,
			},
			subMarking:{
				type:Array,
				default:() => {
					return []
				}
			},
			loading:{
				type: Boolean,
				default: false,
			}
		},
		data() {
			return {
				imgWidth: 0,
				imgHeight: 0,
				defaultWidth: 500,
				showLoading: true,
				currIndex: -1,
				flagData: this.flag,
			}
		},
		watch: {
			url(newValue, oldValue) {
				if (newValue != oldValue)
				{ 
					this.showLoading = true
					this.$emit('imageLoadedChange', false)
				}
			},
			markingIndex(newValue, oldValue) {
				if (newValue != oldValue && newValue >= 0) {
					this.currIndex = newValue
				}
			},
			pageScale(newValue, oldValue) {
				if (newValue != oldValue && newValue >= 0) {
					this.getDefaultWidth()
				}
			},
			flag:{
				deep:true,
				handler(v){
					this.flagData=v
				}
			},
			flagData:{
				deep:true,
				handler(v){
					this.$emit('update:flag',v)
				}
			},
			// loading:{
			// 	deep:true,
			// 	handler(v){
			// 		this.showLoading=v
			// 	}
			// }
		},
		created() {
			this.currIndex = this.markingIndex
		},
		methods: {
			getBoxCSS() {
				let str = ''
				//if (this.showLoading) str += ' loading'
				switch (this.tool) {
					case 'right':
						str += ' cursor-right'
						break
					case 'error':
						str += ' cursor-error'
						break
					case 'text':
						str += ' cursor-text'
						break
					case 'line':
						str += ' cursor-line'
						break
					case 'cut':
						str += ' cursor-cut'
						break
				}
				if (this.rect.length == 1) str += ' only-one-box'
				return str
			},
			getDefaultWidth() {
				// 得到默认宽度
				// let imgS = parseFloat(this.imgWidth) / parseFloat(this.imgHeight)
				// let boxWidth=parseFloat(this.$refs.KSIMG.clientWidth)
				// let boxHeight=parseFloat(this.$refs.KSIMG.clientHeight)
				// let boxS= boxWidth / boxHeight
				this.defaultWidth = this.imgWidth * this.pageScale

				// 有问题
				// if(this.full){
				//     let newPageScale=1
				//     if(boxS<=imgS){
				//         if(this.imgHeight>boxHeight){
				//             newPageScale=boxHeight / parseFloat(this.imgHeight)
				//             this.defaultWidth=this.imgWidth * newPageScale
				//         }
				//         else{
				//             newPageScale=boxWidth / parseFloat(this.imgWidth)
				//             this.defaultWidth=boxWidth
				//         }
				//     }
				//     else{
				//         if(this.imgHeight>boxHeight){
				//             newPageScale=boxHeight / parseFloat(this.imgHeight)
				//             this.defaultWidth=this.imgWidth * newPageScale
				//         }
				//         else{
				//             newPageScale=boxHeight / parseFloat(this.imgHeight)
				//             this.defaultWidth=this.imgWidth * newPageScale
				//         }
				//     }

				//     this.$emit('update:pageScale', newPageScale)
				// }
			},
			load(data) {
				this.imgWidth = data.width
				this.imgHeight = data.height

				this.getDefaultWidth()
				this.showLoading = false
				this.$emit('imageLoadedChange', true)
			},
			indexChange(i) {
				this.currIndex = i
				this.$emit('markingIndexChange', i)
			},
			boxMouseDown(data, mouseBtn) {
				// 如果批注功能关闭，禁止添加新批注
				if (!this.showAnnotations) {
					return
				}

				let c =this.subMarking[this.currIndex].step
				if (this.calcModel) {
					// 累加模式
					if (data.tool == 'error') c = ''
				} else {
					// 累减模式
					if (data.tool == 'right') c = ''
					if (data.tool == 'error') c = -this.subMarking[this.currIndex].step
				}
				if (mouseBtn == 2) c = ''
				let item = {
					type: data.tool,
					index: data.index,
					caption: c,
					x: data.x,
					y: data.y,
				}
				this.flagData.push(item)
				if (mouseBtn == 0) this.$emit('markingScoreMethod', item)
			},
			flagClick(data) {
				// 如果批注功能关闭，禁止修改批注
				if (!this.showAnnotations) {
					return
				}

				let curr = this.flagData[data.flagIndex]
				let oldValue = curr.caption
				if (data.tool == 'right') {
					if (data.currType == 'right') {
						curr.type = 'righterror'
						let tmpScore = accDiv(parseFloat(curr.caption || 0), 2)
						curr.caption = tmpScore + ''
					} else if (data.currType == 'righterror') {
						curr.type = 'error'
						curr.caption = ''
						this.flagData.splice(data.flagIndex, 1)
					} else if (data.currType == 'error') {
						curr.caption = ''
						this.flagData.splice(data.flagIndex, 1)
					}

					this.$emit('markingScoreMethodByFlag', curr, oldValue, data.tool)
				} else if (data.tool == 'error') {
					if (data.currType == 'error') {
						curr.type = 'righterror'
						let tmpScore = accDiv(parseFloat(curr.caption || 0), 2)
						curr.caption = tmpScore + ''
					} else if (data.currType == 'righterror') {
						curr.type = 'error'
						curr.caption = ''
						this.flagData.splice(data.flagIndex, 1)
					} else if (data.currType == 'right') {
						curr.caption = ''
						this.flagData.splice(data.flagIndex, 1)
					}

					this.$emit('markingScoreMethodByFlag', curr, oldValue, data.tool)
				} else if (data.tool == 'cut') {
					curr.caption = ''
					this.flagData.splice(data.flagIndex, 1)
					if (data.currType == 'error' || data.currType == 'righterror' || data.currType == 'right') {
						this.$emit('markingScoreMethodByFlag', curr, oldValue, data.tool)
					}
				}
			},
			addText(data) {
				// 如果批注功能关闭，禁止添加新批注
				if (!this.showAnnotations) {
					return
				}

				let item = {
					type: data.tool,
					index: data.index,
					caption: '',
					x: data.x,
					y: data.y,
				}
				this.flagData.push(item)
				console.log(this.flag)
			},
			// getFlag(){
			// 	console.log(this.flagData)
			// }
		},
	}
</script>

<style lang="scss" scoped>
	.KSIMG-box {
		height: 100%;
		width: 100%;
		overflow: auto;
		padding: 6px;

		&::-webkit-scrollbar {
			/*滚动条整体样式*/
			width : 8px;  /*高宽分别对应横竖滚动条的尺寸*/
			height: 8px;
		}
		&:hover::-webkit-scrollbar-thumb:hover {
			background-color: #cfcfcf;
		}
  		&::-webkit-scrollbar-thumb {
			/*滚动条里面小方块*/
			border-radius: 2px;
			// box-shadow   : inset 0 0 2px rgba(0, 0, 0, 0.2);
			background   : #bebebe;
    	}
		&::-webkit-scrollbar-track {
			/*滚动条里面轨道*/
			// box-shadow   : inset 0 0 2px rgba(0, 0, 0, 0.2);
			border-radius: 2px;        
			background   : #636363;
		}
	}

	.KSIMG {
		position: relative;
		height: auto;
		width: auto;
	//	min-width: 100%;
		display: inline-block;
		user-select: none;
		-webkit-user-select: none;
		-ms-user-select: none;
		-moz-user-select: none;
		-ms-touch-select: none;
	}



	/* 当批注功能关闭时，显示禁用样式 */
	.KSIMG.annotations-disabled {
		cursor: not-allowed;
	}

	@-webkit-keyframes fadein {
		0% {
			opacity: 0;
		}
		100% {
			opacity: 1;
		}
	}

	@keyframes fadein {
		0% {
			opacity: 0;
		}
		100% {
			opacity: 1;
		}
	}

	.KSIMG img {
		-webkit-animation-name: fadein;
		animation-name: fadein;
		-webkit-animation-timing-function: ease-in;
		animation-timing-function: ease-in;
		-webkit-animation-duration: 0.1s;
		animation-duration: 0.1s;
	}

	.cursor-right {
		cursor: url('./images/right.png'), auto;
	}

	.cursor-error {
		cursor: url('./images/error.png'), auto;
	}

	.cursor-text {
		cursor: text;
	}

	.cursor-line {
		cursor: crosshair;
	}

	.cursor-cut {
		cursor: url('./images/cut.png'), auto;
	}

	.KSIMG-box .loading {
		position: absolute;
		z-index: 10000;
		height: 100%;
		width:100%;
		background: url(./images/loading.gif) no-repeat center;
	}
</style>
