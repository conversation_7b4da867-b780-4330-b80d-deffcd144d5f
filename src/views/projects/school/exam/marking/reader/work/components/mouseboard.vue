<template>

<VueDragResize
    v-if="showBox && score>0" 
    :is-draggable="true"
    :is-resizable="false" 
    :parent-limitation="true" 
    :x="left" :y="top" w="auto" h="auto" :z="1006" 
    :is-active="false"
    style="outline:none;"
    drag-handle=".mouseboard .drag,.mouseboard .drag i" 
    @resizing="resize"
    @dragging="resize"
    >
    <div class="mouseboard en-noselect">
        <div class="pull-left drag">
            <i class="en-icon el-icon-more"></i>
        </div> 
        <div class="score-tool">
            给分步长:
            <select v-model="step" style="width: 60px" size="small" @change="stepChange">
                <option :value="5"> 5</option>
                <option :value="4"> 4</option>
                <option :value="3"> 3</option>
                <option :value="2"> 2</option>
                <option :value="1"> 1</option>
                <option :value="0.5"> 0.5</option>
                <option :value="0.25"> 0.25</option>
                <option :value="0.1"> 0.1</option>
            </select>
        </div>
        <div class="keybox">
            <div class="content">
                <div class="box" >
                    <button v-if="imageLoaded" size='small' @click="btnClick('full')">满分</button>
                    <button v-else size='small' disabled>满分</button>
                </div>
                <div class="box" >
                    <button v-if="imageLoaded" size='small' @click="btnClick('0')">零分</button>
                    <button v-else size='small' disabled>零分</button>
                </div>
                <div v-for="i in getCount()" :key="'key'+i" class="box" >
                    <button v-if="imageLoaded" size='small' @click="btnClick(getKeyText(i))">{{getKeyText(i)}}</button>
                    <button v-else size="small" disabled>{{getKeyText(i)}}</button>
                </div>
            </div>
        </div>
       
    </div>
</VueDragResize>
   
</template>

<script>
import VueDragResize from '@/extra/vue-drag-resize'
import {accMul} from './operation'

export default {
    components: {
        VueDragResize
    },
    props:{
        score:{
            type:Number,
            default:0
        },
        showBox:{
            type:Boolean,
            default:false
        },
        stepScore:{
            type:Number,
            default:1
        },
        imageLoaded:{
            type:Boolean,
            default:false
        }
    },
    data(){
        return{
            width: 0,
            height: 0,
            top: 0,
            left: 0,
            step:1,
        }
    },
    watch:{
        stepScore(newValue,oldValue){
            if(newValue!=oldValue){
                this.step=newValue
            }
        },

    },
    created(){
        let width=parseInt(document.body.clientWidth) - 350
        let _left=sessionStorage.getItem("mouseboard-left") || width
        let _top=sessionStorage.getItem("mouseboard-top") || 385
        _left=parseInt(_left)
        _top=parseInt(_top)
        this.left = _left
        this.top = _top
        this.step=this.stepScore
        if(!this.step) this.step=1
    },
    methods:{
        resize(newRect) {
            this.top = newRect.top;
            this.left = newRect.left;
            sessionStorage.setItem("mouseboard-left",this.left)
            sessionStorage.setItem("mouseboard-top",this.top)
        },
        getCount(){
            return Math.ceil(this.score / parseFloat(this.step))
        },
        getKeyText(i){
            let a=accMul(parseFloat(this.stepScore),i) 
            if( a>this.score) a=this.score
            return  a
        },
        stepChange(){
            this.$emit("stepScoreChange",this.step)
        },
        btnClick(v){
            if(!this.imageLoaded) return
            this.$emit("keyClick",v)
        }
    },
   
}
</script>

<style lang="scss" scoped>
    .mouseboard{
        z-index: 100;
        width: 272px;
        height: auto;
        background: #2f4050;
        color: #ccc;
        border-radius: 4px;
        box-shadow:0 0 4px #888;
        padding: 8px 4px 0 4px;
        overflow: hidden;
        transition: all .2s ease-in 0ms;
    }

    .drag{
        float: left;
        padding: 0;
        cursor: move;
        color: orange;
        text-align: center;
        width:20px;
        transform: rotate(90deg);

        i{
            padding: 0 !important;
        }
    }

    .mouseboard .score-tool{
        float:right;
        padding-right: 4px;
        line-height: 2;
    }

    /* .mouseboard .score-tool .ant-select-selection--single i{
        margin-top: -0.325rem;
    } */

    .mouseboard .content{
        clear: both;
        padding: 6px 4px;
        display: -webkit-box;
        display: -ms-flexbox;
        display:flex; 
        flex-direction: row;
        flex-wrap: wrap;
        justify-content: flex-start;
        align-content: center;
        align-items: center;
    }

    .mouseboard .content .box{
        float: left;
        display: block;
        width:62px;
        height: 34px;
        padding: 4px;
        border-radius: 2px;
    }

     .mouseboard .content .box button{
        width:100%;
        color: #888;
        background: #4b5864;
        border-color: #888;
        border-width: 0px;
        &:disabled{
            cursor:not-allowed;
            &:hover{
                color: #888;
            }
        }
    }

    .mouseboard .content .box button:hover{
        color: #fff;
        background: #4b5864;
        border-color: #efefef;
    }

    .mouseboard select option{
        background: #4b5864;
        color: #ccc;
        text-align: center;
    }

    

    .keybox{
        max-height: 320px;
        width: 100%;
        overflow-y: auto
    }

    .keybox::-webkit-scrollbar {
        /*滚动条整体样式*/
        width : 8px;  /*高宽分别对应横竖滚动条的尺寸*/
    }
  .keybox::-webkit-scrollbar-thumb {
        /*滚动条里面小方块*/
        border-radius: 2px;
        box-shadow   : inset 0 0 2px rgba(0, 0, 0, 0.2);
        background   : #64696e;
    }
  .keybox::-webkit-scrollbar-track {
        /*滚动条里面轨道*/
        box-shadow   : inset 0 0 2px rgba(0, 0, 0, 0.2);
        border-radius: 2px;        
        background   : #3e4852;
    }
    


   

.vdr.active:before {
    outline: none;
}
</style>