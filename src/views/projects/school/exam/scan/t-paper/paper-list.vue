<template>
    <div class="paper-list">
        <div class="tool-bar">
            <div>
                <el-button :disabled="!selectRows.length" @click="movePaper">移动</el-button>
                <el-button :disabled="!selectRows.length" @click="rotatePaper">旋转</el-button>
                <el-button :disabled="!selectRows.length" @click="flipPaper">翻转</el-button>
                <el-button :disabled="!selectRows.length" @click="deletePaper">删除</el-button>
            </div>
            <div class="search-box-key">
                <el-input v-model="page.key" placeholder="请输入关键字查询">
                    <template slot="append">
                        <div class="search-button" @click="fetchData()">
                            <i class="el-icon-search" ></i>搜索
                        </div>
                    </template>
                </el-input>
            </div>

        </div>


        <div ref="mybox" v-loading="tableSetting.listLoading" class="table-box">
       	    <el-table
				ref="myTable"
				:border="tableSetting.border"
				:data="paperData.info"
				:height="height"
				:size="tableSetting.lineHeight"
				:stripe="tableSetting.stripe"
				@selection-change="setSelectRows"
			>
                <el-table-column align="center" type="selection" width="47" />
                <el-table-column label="试卷包" width="140" prop="scanPackageCode">
                    <template #default="{ row }">
                        <span v-if="row.scanPackageCode=='manual'">手动上传包</span>
                        <span v-else>  {{ row.scanPackageCode }}</span>
                    </template>
                </el-table-column>
                <el-table-column label="序号" align="center" width="80" prop="scanPaperIndex" />
				<el-table-column label="考号" align="center" width="180" prop="studentKH"/>
				<el-table-column label="识别情况" align="center" width="200" prop="recognition">
                    <template #default="{ row }">
                        <div class="recognition-box">
                            <div>
                                <ut-icon v-if="row.recognitionKH" icon="checkbox-circle-fill" color="green" />
                                <ut-icon v-else icon="close-circle-line" color="red" />
                                <span class="title">考号</span>
                            </div>
                            <div>
                                <ut-icon v-if="row.recognitionXZ" icon="checkbox-circle-fill" />
                                <ut-icon v-else icon="close-circle-line" />
                                <span class="title">选择题</span>
                            </div>
                            <div>
                                <ut-icon v-if="row.recognitionZGXZ" icon="checkbox-circle-fill" />
                                <ut-icon v-else icon="close-circle-line" />
                                <span class="title">选做题</span>
                            </div>
                        </div>
                    </template>
                </el-table-column>
				<el-table-column label="缺考违纪" align="center" width="100" prop="qkwj">
                    <template #default="{ row }">
                        <div class="qkwj-box">
                            <div v-if="row.isQK">
                                <span class="title">缺考</span>
                            </div>
                            <div v-if="row.isWJ">
                                <span class="title">违纪</span>
                            </div>
                        </div>
                    </template>
                </el-table-column>
                <el-table-column label="上传时间" align="center" width="180" prop="uploadTime" />
                <el-table-column label="上传IP" align="center" width="120" prop="uploadIP" />
                <el-table-column label="上传用户" align="center" width="100" prop="uploadUser" />
                <el-table-column label="试卷" align="center" width="80" fixed="right">
                    <template #default="{ row }">
                        <el-button @click="view(row)">查看</el-button>
                    </template>
                </el-table-column>
				<template #empty>
					<el-image class="ut-data-empty" :src="require('@/assets/empty_images/data_empty.png')" />
				</template>
			</el-table>
        </div>
        <el-pagination
			background
			:current-page="page.pageindex"
			layout="total, sizes, prev, pager, next, jumper"
			:page-size="page.pagesize"
			:total="paperData.record"
			@current-change="handleCurrentChange"
			@size-change="handleSizeChange"
		/>

        <ut-media-viewer v-if="isShow" :initial-index="0" :init-deg="initDeg" :url-list="urlList" @close=" isShow = false" />

        <ut-modal v-model="showRotateDialog" class="main-wrapper" top="100px" title="图片旋转" width="300px">
		    <dialog-rotate  @close="showRotateDialog=false" @save="rotateSave"/>
        </ut-modal>

        <ut-modal v-model="showMoveDialog" class="main-wrapper" top="100px" title="试卷移动" width="500px">
		    <dialog-move  @close="showMoveDialog=false" @save="moveSave"/>
        </ut-modal>
    </div>


</template>

<script>
import { mapGetters } from 'vuex'
import DialogRotate from './rotate.vue'
import DialogMove from './move.vue'


export default {
	components: {
        DialogRotate,
        DialogMove,
	},
	mixins: [],
	props: {
        parentData:{
            type:String,
            default:''
        },
	},
	data() {
		return {
            height:0,
            page: {
				pageindex: 1,
				pagesize: 100,
				key: '',
			},
           tableSetting: {
				border: true,
				listLoading: false,
				height: this.height,
				lineHeight: 'small',
				stripe: false,
			},
            paperData:{},
            selectRows:[],
            urlList:[],
            isShow:false,
            initDeg:0,
            showRotateDialog:false,
            showMoveDialog:false,
		}
	},
	computed: {
		...mapGetters({
			school: 'comm/comm',
			exam:'exam/exam',
			course:'exam/course',
		}),
	},
	watch:{
        parentData:{
            deep:true,
            handler(){
                this.page.pageindex=1
                this.fetchData()
            }
        }
    },
    mounted(){
        this.$nextTick(()=>{
             this.handleResize()
        })
        window.addEventListener('resize', this.handleResize)

    },
    destroyed(){
        window.removeEventListener('resize',this.handleResize)
    },
	created() {
	},
    beforeMount(){
        this.fetchData()
    },
	methods: {
        handleSizeChange(val) {
			this.page.pagesize = val
			this.fetchData()
		},
		handleCurrentChange(val) {
			this.page.pageindex = val
			this.fetchData()
		},
		setSelectRows(val){
            this.selectRows = val
			this.$emit('selectRows', val)
        },
        handleResize(){
            if(this.$refs.mybox && this.$refs.mybox.clientHeight){
                this.height=this.$refs.mybox.clientHeight
            }
        },
        async fetchData(pageReq){
            if (pageReq) this.page = pageReq
            this.tableSetting.listLoading=true
            const {data} = await this.$ut.api('schoolscan/package/paper/list',{
				examId:this.exam.id,
				examCourseId:this.course.id,
				packageCode:this.parentData,
				...this.page,
			})
            this.tableSetting.listLoading=false
			this.paperData = data
        },
        async view(item){
            const {data}= await this.$ut.api('schoolscan/package/paper/info',{
                examId:this.exam.id,
				examCourseId:this.course.id,
				paperId:item.id,
            })
            this.urlList=[]
            this.initDeg=0
            if(data.papers){
                this.urlList=data.papers.map(u=>u.url)
                if(data.papers.length) this.initDeg=data.papers[0].rotate
            }
            this.isShow=true
        },
        movePaper(){
            this.showMoveDialog=true
            // this.$baseConfirm('是否确认移动到其它科目?移动后将删除阅卷记录。', null, async () => {
            //     this.showMoveDialog=true
            // })
        },
        rotatePaper(){
            // this.$baseConfirm('是否旋转试卷图片?', null, async () => {
                this.showRotateDialog=true
            // })
        },
        rotateSave(deg){
            this.$ut.api('schoolscan/paper/rotate',{
                examId:this.exam.id,
                examCourseId:this.course.id,
                angle:deg,
                paperIds:this.selectRows.map(u=>u.id)
            }).then(()=>{
                this.$baseMessage('旋转操作成功', 'success', 'ut-hey-message-success')
                this.showRotateDialog=false
            })
        },
        flipPaper(){
            this.$baseConfirm('是否正反面翻转试卷?翻转后选项等需要重新识别。', null, async () => {
                this.$ut.api('schoolscan/paper/flip',{
                examId:this.exam.id,
                examCourseId:this.course.id,
                    paperIds:this.selectRows.map(u=>u.id)
                }).then(()=>{
                    this.$baseMessage('前后翻转操作成功', 'success', 'ut-hey-message-success')
                    this.showRotateDialog=false
                })
            })
        },
        deletePaper(){
            this.$baseConfirm('是否删除到回收站?', null, async () => {
            })
        },
        moveSave(){
            this.$baseConfirm('是否确认移动到其它科目?移动后将删除阅卷记录。', null, async () => {
                this.showMoveDialog=false
                // this.showMoveDialog=true
            })
        }


	},
}
</script>

<style lang="scss" scoped>

.search-box-key{
    width:300px;
    margin-bottom: 8px;
}

.paper-list{
    height: 100%;
    padding: 8px;
    background-color: #fff;
    border-radius: 4px;
    display: flex;
    flex-direction: column;

    .table-box{
        position: relative;
        flex: 1;
        :deep(.el-table){
            position: absolute;
            top: 0;
            right: 0;
            left: 0;
            bottom: 0;
        }

    }

    .tool-bar{
        display: flex;
        justify-content: space-between;
    }


}

// .clearfix:before,
// .clearfix:after {
// 	display: table;
// 	content: '';
// }
// .clearfix:after {
// 	clear: both;
// }

.recognition-box,.qkwj{
    display: flex;
    align-items: center;
    justify-content: space-between;

    .title{
        font-size: 12px;
    }
}

.ri-close-circle-line::before{
    color: gray;
}

.ri-checkbox-circle-fill::before{
    color: $base-color-blue;
}

.search-button{
    cursor: pointer;
}

</style>
