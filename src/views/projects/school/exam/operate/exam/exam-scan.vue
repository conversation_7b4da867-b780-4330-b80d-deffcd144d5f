<template>
	<el-card class="exam-scan" shadow="hover">
		<template #header>
			<ut-icon icon="polaroid-2-line" />
			扫描仪设置
			<el-tag class="card-header-tag" @click="open">扫描参数修改</el-tag>
		</template>
		<el-scrollbar>
			<el-descriptions class="margin-top" :column="2" border>
				<el-descriptions-item>
					<template slot="label">
						<i class="el-icon-user"></i>
						分辨率：
					</template>
					{{detail.dpi}}DPI
				</el-descriptions-item>

				<el-descriptions-item>
					<template slot="label">
						<i class="el-icon-user"></i>
						图像类型：
					</template>
					<span v-if="detail.color==1">黑白</span>
					<span v-if="detail.color==2">灰色</span>
					<span v-if="detail.color==3">彩色</span>
				</el-descriptions-item>

				<el-descriptions-item>
					<template slot="label">
						<i class="el-icon-user"></i>
						纸张来源：
					</template>
					<span v-if="detail.source==1">单页</span>
					<span v-if="detail.source==2">双页</span>
				</el-descriptions-item>

				<el-descriptions-item>
					<template slot="label">
						<i class="el-icon-user"></i>
						图像旋转：
					</template>
					{{detail.rotation}}°
				</el-descriptions-item>

				<el-descriptions-item>
					<template slot="label">
						<i class="el-icon-user"></i>
						自动裁切：
					</template>
					<span v-if="detail.autoBorderDetection">启用</span>
					<span v-else>不启用</span>
				</el-descriptions-item>

				<el-descriptions-item>
					<template slot="label">
						<i class="el-icon-user"></i>
						歪斜校正：
					</template>
					<span v-if="detail.autoDeskew">启用</span>
					<span v-else>不启用</span>
				</el-descriptions-item>
			</el-descriptions>
			<div class="remark">注意：默认设置为最佳效果设置，无特殊要求，尽量不要修改。</div>
			<div v-if="examType==2 || examType==3 || examType==4" class="remark remark-color">所有参与学校将自动按此设置上传试卷，无需手动设置扫描仪</div>
		</el-scrollbar>
		<el-dialog v-dialogDrag :close-on-click-modal="false" title="扫描仪设置" :visible.sync="dialogFormVisible" width="500px" append-to-body @close="close">
			<edit-scan ref="scan" />
			<template #footer>
				<el-button type="primary" @click="save">确 定</el-button>
				<el-button @click="close">取 消</el-button>
			</template>
		</el-dialog>
	</el-card>
</template>

<script>
import { mapGetters } from 'vuex'
import  EditScan from  '../edit-scan'
export default {
	components: {
		EditScan,
	},
	props:{
		detail: {
			type: Object,
			default: () => {},
		},
		examId: {
			type: String,
			default: '',
		},
		examType: {
			type: Number,
			default: 0,
		},
	},
	data() {
		return {
			dialogFormVisible:false,
		}
	},
	computed: {
		...mapGetters({
			school: 'comm/comm',
		}),
	},
	methods: {
		close() {
			this.dialogFormVisible = false
		},
		open(){
			this.dialogFormVisible=true
			this.$nextTick(()=>{
				this.$refs.scan.form=Object.assign({}, this.detail)
			})
		},
		save(){
			let params=this.$refs.scan.form
			params.schoolId=this.school.id
			params.examId=this.examId
			this.$ut.api('schoolexam/setScan',params).then(() => {
				this.$emit('fetch-data')
				this.close()
			})
		}
	},
}
</script>

<style lang="scss" scoped>
.card-header-tag{
	transition: all 0.3s;
	display: none;
}
.exam-scan:hover .card-header-tag{
	display: inherit;
	cursor: pointer;
	transition: all 0.3s;
}

.remark{
	font-size: 12px;
	padding: 10px 6px 0;
	color: #a0a0a0;
}

.remark-color{
	color: $base-color-blue;
}
</style>
