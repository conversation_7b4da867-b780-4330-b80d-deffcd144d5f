<template>
	<el-form ref="form" label-width="100px" :model="form" :rules="rules" class="ut-form">
		<el-row :gutter="20">
			<el-col :span="12">
				<el-form-item label="分辨率：" prop="dpi">
					<el-select v-model="form.dpi" placeholder="请选择">
						<el-option label="100" :value="100" />
						<el-option label="150" :value="150" />
						<el-option label="200" :value="200" />
						<el-option label="300" :value="300" />
					</el-select>
				</el-form-item>
			</el-col>
			<el-col :span="12">
				<el-form-item label="图像类型：" prop="color">
					<el-select v-model="form.color" placeholder="请选择">
						<el-option label="黑白" :value="1" />
						<el-option label="灰色" :value="2" />
						<el-option label="彩色" :value="3" />
					</el-select>
				</el-form-item>
			</el-col>
			<el-col :span="12">
				<el-form-item label="纸张来源：" prop="source">
					<el-select v-model="form.source" placeholder="请选择">
						<el-option label="单面" :value="1" />
						<el-option label="双面" :value="2" />
					</el-select>
				</el-form-item>
			</el-col>

			<el-col :span="12">
				<el-form-item label="图像旋转：" prop="rotation">
					<el-select v-model="form.rotation" placeholder="请选择">
						<el-option label="0°" :value="0" />
						<el-option label="90°" :value="90" />
						<el-option label="180°" :value="180" />
						<el-option label="270°" :value="270" />
						<el-option label="-90°" :value="-90" />
					</el-select>
				</el-form-item>
			</el-col>
			<el-col :span="12">
				<el-form-item label="自动裁切：" prop="autoBorderDetection">
					<el-switch v-model="form.autoBorderDetection" />
				</el-form-item>
			</el-col>
			<el-col :span="12">
				<el-form-item label="歪斜校正：" prop="autoDeskew">
					<el-switch v-model="form.autoDeskew" />
				</el-form-item>
			</el-col>
		</el-row>
        <div>
            <el-button  round plain size="mini" @click="setDefault">使用推荐值</el-button>
        </div>
	</el-form>
</template>

<script>
import { mapGetters } from 'vuex'
export default {
	components: {},
	props: {
	},
	data() {
		return {
			form: {
				dpi: 200,
				color: 2,
				source: 2,
				rotation: -90,
				autoBorderDetection: true,
				autoDeskew: true,
			},
			rules: {
				type: [{ required: true, trigger: 'blur', message: '请选择阶段' }],
				grade: [{ required: true, trigger: 'blur', message: '请选择入学年份' }],
				name: [{ required: true, trigger: 'blur', message: '请填写考试名称' }],
			},
		}
	},
	computed: {
		...mapGetters({
			unit: 'comm/comm',
		}),
	},
	watch:{

	},
	created() {},
	methods: {
        setDefault(){
            this.form.dpi = 200
            this.form.color =  2
            this.form.source = 2
            this.form.rotation = -90
            this.form.autoBorderDetection = true
            this.form.autoDeskew = true
        },
	},
}
</script>

<style lang="scss" scoped>
.edit-button-group {
	display: block;
	margin: 20px auto;
	text-align: center;
}
</style>
