<template>
	<div v-loading="listLoading" class="index-container">
		<el-row :gutter="12">
			<exam-header :detail="dataInfo" :exam-id="exam.id" @fetch-data="fetchData"/>
			<el-col :lg="12" :md="24" :sm="24">
				<exam-course :detail="dataInfo.courses" :exam-id="exam.id" @fetch-data="fetchData" />
			</el-col>
			<el-col :lg="12" :md="24" :sm="24">
				<exam-class :detail="dataInfo" @fetch-data="fetchData" />
			</el-col>
			<el-col :lg="12" :md="24" :sm="24">
				<exam-scan :detail="dataInfo.scan || {}" :exam-id="exam.id" :exam-type="dataInfo.examType" @fetch-data="fetchData" />
			</el-col>
			<el-col :lg="12" :md="24" :sm="24">
				<exam-menu />
			</el-col>
		</el-row>

		<!-- <edit-dialog :show-dialog="false" /> -->
		<!-- <add-dialog :show-dialog="true" /> -->
		<!-- <edit-scan /> -->
		<!-- <edit-base /> -->
	</div>
</template>

<script>
import { mapGetters } from 'vuex'

import ExamHeader from './exam/exam-header'
import ExamScan from './exam/exam-scan.vue'
import ExamCourse from './exam/exam-course'
import ExamClass from './exam/exam-class'
// import ExamArrangeCode from './exam/exam-arrange-code'
import ExamMenu from './exam/exam-menu'
// import ExamStudent from './exam/exam-student'

// import PageHeader from '@/views/index/oldComponents/PageHeader'
// import EditBase from './edit-base'
// import EditDialog from './edit-dialog'
// import AddDialog from './add-dialog'
// import EditScan from './edit-scan.vue'
// import ReceiptTable from './receipt-table'
export default {
	components: {
		ExamHeader,
		ExamScan,
		ExamCourse,
		ExamClass,
		// ExamArrangeCode,
		ExamMenu,
		// ExamStudent,
		// PageHeader,
		// EditScan,
		// EditDialog,
		// ReceiptTable,
		// AddDialog,
	},
	props: {},
	data() {
		return {
			listLoading:false,
			dataInfo:{},

		}
	},
	computed: {
		...mapGetters({
			school: 'comm/comm',
			exam:'exam/exam',
		}),
	},
	watch:{
		'exam.id':{
			deep:true,
			handler(){
				this.fetchData()
			}
		}
	},
	created() {
		this.$on("ExamCourse", this.handleSetStep);

		this.fetchData()
		// console.log(this.$route.query.id)
	},
	methods: {
		fetchData() {
			this.listLoading = true
			let params={
				schoolId: this.school.id,
				examId:this.exam.id,
			}
			this.$ut.api('schoolexam/info', params).then((res) => {
				this.dataInfo = res.data
				this.listLoading = false
			})
		},
		handleSetStep(active, form) {
			this.active = active
			if (form) this.form = Object.assign(this.form, form)
		},
	},
}
</script>

<style lang="scss" scoped>
.index-container {
	padding: 8px 0 0 0 !important;

	:deep() {
		.exam-scan,
		.exam-course,
		.exam-class {
			min-height: 248px;
		}

		.el-card {
			.el-card__header {
				position: relative;

				.card-header-tag {
					position: absolute;
					top: 15px;
					right: $base-margin;
				}

				> div > span {
					display: flex;
					align-items: center;

					i {
						margin-right: 3px;
					}
				}
			}

			.el-card__body {
				position: relative;

				.echarts {
					width: 100%;
					height: 127px;
				}

				.card-footer-tag {
					position: absolute;
					right: $base-margin;
					bottom: 15px;
				}
			}
		}

		.bottom {
			padding-top: 20px;
			margin-top: 5px;
			color: #595959;
			text-align: left;
			border-top: 1px solid $base-border-color;
		}
	}
}
</style>
