<template>
    <div>
        <el-card>
            <!-- <div class="text-line">编排方式：</div> -->
            <div class="exam-count text-line">
                <span class="count">考生总数：{{ studentCount }}人</span>
                <span>班级数：{{ classCount }}</span>
            </div>
        </el-card>
        <el-card>
            <div class="box-item">
                <span>考场增加方案：</span>
                <el-select v-model="type">
                    <el-option label="单考场人数" :value="1"/>
                    <el-option label="单考场人数" :value="2"/>
                </el-select>
                <el-input v-model="singleCount" type="number" :placeholder="type==1?'请填写每考场人数':'请填考场数量'" style="width: 140px;" />
            </div>
            <div v-if="studentCount && singleCount">
                <div class="content-box">
                    <el-radio v-model="metodType" :label="1">方案1</el-radio>
                    <div class="remark">
                        共{{ roomCount }}个考场
                        <span v-if="roomFront1">，前{{ roomFront1 }}个考场每间{{ roomFrontCount1 }}人</span> 
                        <span v-if="roomAfter1">，后{{ roomAfter1 }}个考场每间{{ roomAfterCount1 }}人</span>
                    </div>
                </div>
                <div v-if="type==1" class="content-box">
                    <el-radio v-model="metodType" :label="2">方案2</el-radio>
                    <div class="remark">
                        共{{ roomCount }}个考场
                        ，前{{ roomFront2 }}个考场每间{{ roomFrontCount2 }}人
                        <span v-if="roomAfter2">，后{{ roomAfter2 }}个考场每间{{ roomAfterCount2 }}人</span>
                    </div>
                </div>
            </div>
        </el-card>
        <el-card>
            <div class="box-item">
                <span>考场座位设置：</span>
                <el-select v-model="roomType">
                    <el-option :label="'顺序座位（1~'+singleCount+'）'" :value="1"/>
                    <el-option label="n排m座" :value="2"/>
                    <el-option label="n组m座" :value="3"/>
                </el-select>
            </div>
            <div v-if="roomType!=1" class="box-item seat">
                <span>共</span>
                <el-input v-model="groupCount" type="text" class="input"/>
                <span v-if="roomType==2">排</span>
                <span v-else>组</span>
                <el-input v-model="posCount" type="text" class="input"/>座
            </div>
        </el-card>
        <div class="ut-edit-footer">
            <el-button v-if="studentCount" type="primary" @click="save">{{ lang('确定') }}</el-button>
            <el-button v-else  disabled >{{ lang('无考生，无法编排') }}</el-button>
            <el-button @click="$emit('close')">{{ lang('取消') }}</el-button>
        </div>
    </div>
</template>
<script>

// import { mapGetters } from 'vuex'
import { lang } from '@/common/utils/i18n'
Number.prototype.az = function(n = 2) {
	let s = "";
	for (let i = 1; i < n; i++) {
		s += '0';
	}
	return (s + this).slice(-1 * n);
}

export default {
    props:{
        studentCount:{
            type:Number,
            default:0,
        },
        classCount:{
            type:Number,
            default:0,
        }
    },
    data() {
		return {
            type:1,
            singleCount:'',
            roomType:1,
            groupCount:0,
            posCount:0,
            metodType:1,
        }
    },
    computed:{
        roomCount(){
            let examRoomCount=0
            if(this.type==1){
                examRoomCount = Math.floor(this.studentCount / this.singleCount)
                if (this.studentCount % this.singleCount > 0) examRoomCount++
            }else if(this.type==2){
                examRoomCount = this.singleCount
            }
           return examRoomCount
        },
        roomFront1(){
            let examRoomCount=0
            if(this.type==1){
                return  examRoomCount = Math.floor(this.studentCount / this.singleCount)   
            }else if(this.type==2){
                let  normalNum = Math.floor(this.studentCount / this.singleCount)
                examRoomCount= this.studentCount - ((normalNum) * this.singleCount)
            }
           return examRoomCount
        },
        roomFront2(){
            let examRoomCount=0
            if(this.type==1){
                let moreStudent = this.studentCount - ((this.roomCount - 1) * this.singleCount)
                examRoomCount = Math.floor(moreStudent / this.singleCount)  
                if (moreStudent % this.singleCount > 0) examRoomCount++      
            }
            
            return examRoomCount
        },
        roomFrontCount1(){
            let count=0
            if(this.type==1){
                count = this.singleCount
            }else if(this.type==2){
                count = Math.floor(this.studentCount / this.singleCount)
                if (this.studentCount % this.singleCount > 0) count++;
            }

            if(count>this.studentCount) count=this.studentCount
            return count
        },
        roomFrontCount2(){
            let count=0
            if(this.type==1){
                count = this.singleCount
            }
            
            if(count>this.studentCount) count=this.studentCount
            return count
        },
        roomAfter1(){
            let examRoomCount=0
            if(this.type==1){
                if (this.studentCount % this.singleCount > 0) examRoomCount++
            }else if(this.type==2){
               examRoomCount=this.roomCount - this.roomFront1
            }
            return examRoomCount
        },
        roomAfter2(){
            let examRoomCount=0
            if(this.type==1){
                examRoomCount=this.roomCount - this.roomFront2
            }
            return examRoomCount
        },
        roomAfterCount1(){
            let count=0
            if(this.type==1){
                count = this.studentCount - (this.roomFront1 * this.singleCount)
            }else if(this.type==2){
                count = Math.floor(this.studentCount / this.singleCount)
            }

            return count
        },
        roomAfterCount2(){
            let count=0
            if(this.type==1){
                count =  this.singleCount-1
            }

            return count
        },
    },
    methods:{
        lang,
        save(){
            if(!this.roomCount) return
            let arr=[]
            for(let i=0;i<this.roomCount;i++){
                arr.push({
                    name:'考场'+(i+1).az(this.roomCount.length),
                    normal:this.roomFrontCount2,
                    type:this.roomType,
                    group:this.groupCount,
                    pos:this.posCount,
                })
            }
            this.$emit('save',arr)
            this.$emit('close')
        }
    }
}
</script>>

<style lang="scss" scoped>
.box-item{
    height: 40px;
}

.box-wrapper{
    height: 350px;
    display: flex;
    flex-direction: column;

    :deep(.el-card){
        &:last-child{
            flex: 1;
        }
    }
}

.content-box{
    min-height: 60px;
    .remark{
        padding-left: 23px;
        line-height: 2;
    }
}
.input{
    text-align: center;
    width:80px;
}
.text-line{
    line-height: 2;
}
.exam-count{
    .count{
        margin-right: 20px;
        display: inline-block;
        min-width: 150px
    }
}

.seat{
    padding-left: 69px;
    :deep(.el-input){
        margin-left: 15px;
        margin-right: 15px;
    }
}
</style>