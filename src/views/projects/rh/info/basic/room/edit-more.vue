<template>
	<div v-loading="loading">
		<el-form ref="form" label-width="110px" :model="form" :rules="rules" class="ut-form">
			<el-row>
				<el-col :span="12">
					<el-form-item label="楼栋名称：" readonly>
						<div>{{ typeTitle }}</div>
					</el-form-item>
				</el-col>
				<el-col :span="12">
					<el-form-item label="楼栋编码：" readonly>
						<div>{{ typeCode }}</div>
					</el-form-item>
				</el-col>
				<el-col :span="12">
					<el-form-item label="房号规则：" prop="codeValue">
						<el-select v-model="codeValue">
							<el-option v-for="e in codeOption" :key="e.value" :label="e.label" :value="e.value" />
						</el-select>
					</el-form-item>
				</el-col>
				<el-col :span="12">
					<el-form-item label="添加方式：" prop="addValue">
						<el-select v-model="addValue">
							<el-option v-for="e in addOption" :key="e.value" :label="e.label" :value="e.value" />
						</el-select>
					</el-form-item>
				</el-col>
				<el-col :span="12">
						<el-form-item label="名称前缀：" prop="roomNameQZ">
							<el-input v-model="form.roomNameQZ" placeholder="名称前缀，没有可不填" type="text" :maxlength="18" />
						</el-form-item>
					</el-col>
					<el-col :span="12">
						<el-form-item label="名称后缀：" prop="roomNameHZ">
							<el-input v-model="form.roomNameHZ" placeholder="名称后缀，没有可不填" type="text" :maxlength="18" />
						</el-form-item>
					</el-col>
				<el-col :span="12">
					<el-form-item v-if="addValue == 1" label="栋总层数：" prop="floorNum">
						<el-input v-model="form.floorNum" placeholder="本栋或单元共有多少层" type="text" :maxlength="18" />
					</el-form-item>
				</el-col>
				<el-col :span="12">
					<el-form-item v-if="addValue == 1" label="每层几间：" prop="roomC">
						<el-input v-model="form.roomC" placeholder="每层有几间房" type="text" :maxlength="18" />
					</el-form-item>
				</el-col>
				<el-col :span="12">
					<el-form-item label="编号前缀：" prop="roomCodeQZ">
						<el-input v-model="form.roomCodeQZ" placeholder="房号前缀，没有可不填" type="text" :maxlength="18" />
					</el-form-item>
				</el-col>
				<el-col :span="12">
					<el-form-item label="编号后缀：" prop="roomCodeHZ">
						<el-input v-model="form.roomCodeHZ" placeholder="房号后缀，没有可不填" type="text" :maxlength="18" />
					</el-form-item>
				</el-col>
				<el-col :span="12">
					<el-form-item label="房间编号位数：" prop="roomCodeWS">
						<el-input v-model="form.roomCodeWS" placeholder="请输入房间编号位数" type="text" :maxlength="18" />
					</el-form-item>
				</el-col>
				<el-col :span="12">
					<el-form-item label="面积：" prop="acreage">
						<el-input v-model="form.acreage" type="text" placeholder="请输入面积" />
					</el-form-item>
				</el-col>
				<el-col :span="12">
					<el-form-item label="房间类型：" prop="roomTypeId">
						<!-- <el-input v-model="form.roomTypeId" type="text" placeholder="请选择型" /> -->
						<el-select v-model="form.roomTypeId" placeholder="请选择房间类型">
							<el-option v-for="item in roomTypeList" :key="item.value" :label="item.label" :value="item.value" />
						</el-select>
					</el-form-item>
				</el-col>
				<el-col :span="12">
					<el-form-item label="床位数量：" prop="bedCount">
						<el-input v-model="form.bedCount" type="number" placeholder="请输入床位数量" />
					</el-form-item>
				</el-col>
				<el-col :span="12">
					<el-form-item v-if="addValue == 2" label="起始编号：" prop="startNo">
						<el-input v-model="form.startNo" placeholder="房间的起始编号" type="text" :maxlength="18" />
					</el-form-item>
				</el-col>
				<el-col :span="12">
					<el-form-item v-if="addValue == 2" label="添加数量：" prop="addCount">
						<el-input v-model="form.addCount" placeholder="需要添加几间房" type="text" :maxlength="18" />
					</el-form-item>
				</el-col>
			</el-row>
		</el-form>

		<div class="ut-edit-footer">
			<el-button type="primary" @click="save">{{ lang('确定') }}</el-button>
			<el-button @click="$emit('close')">{{ lang('取消') }}</el-button>
		</div>
	</div>
</template>

<script>
	import { mapGetters } from 'vuex'
	import { lang } from '@/common/utils/i18n'

	export default {
		components: {},
		props: {
			initData: {
				type: Object,
				default: () => ({}),
			},
			parentData: {
				type: Object,
				default: () => ({}),
			},
		},

		data() {
			return {
				loading: false,
				form: {
					treeType: '',
					treeID: '',
					codeType: 0,
					addType: 1,
					name: '',
					roomNameHZ:'',
					roomNameQZ:'',
					roomCodeQZ: '',
					roomCodeHZ: '',
					roomCodeWS: null,
					floorNum: null,
					roomC: null,
					startNo: null,
					addCount: null,
					acreage: null,
					roomTypeId: '',
					roomTypeName: '',
					roomStateId: '',
					roomStateName: '',
					huxingId: '',
					huxingName: '',
					remark: '',
				},
				typeTitle: '',
				typeCode: '',
				rules: {
					name: [{ required: true, trigger: 'blur', message: '请输入房间名称' }],
					code: [{ required: true, trigger: 'blur', message: '请输入房间编码' }],
				},
				typeList: [],
				roomTypeList: [],
				codeValue: 0,
				codeOption: [
					{
						value: 0,
						label: '默认增加',
					},
					{
						value: 1,
						label: '排除4号房',
					},
				],
				addOption: [
					{
						value: 1,
						label: '单元房/高层',
					},
					{
						value: 2,
						label: '别墅/商铺/其它',
					},
				],
				addValue: 1,
			}
		},
		computed: {
			...mapGetters({
				comm: 'comm/comm',
			}),
		},
		watch: {
			codeValue() {
				this.form.codeType = this.codeValue
			},
			addValue() {
				this.form.addType = this.addValue
			},
			form: {
				handler() {
					if (this.form.isSettlement) this.settleValue = 1
					else this.settleValue = 0
				},
				deep: true,
			},
		},
		beforeMount() {
			// console.log('--',this.parentData)
			this.typeTitle = this.parentData.label
			this.typeCode = this.parentData.code
			this.form.floorId = this.parentData.id
			this.getInfo()
			this.getTypeList()
			this.getRoomTypeList()
		},
		mounted() {},
		methods: {
			lang,
			async getInfo() {
				if (!this.initData.id) return
				this.loading = true
				const { data } = await this.$ut
					.api('rhinfo/room/info', {
						communityId: this.comm.id,
						id: this.initData.id,
					})
					.finally(() => {
						this.loading = false
					})
				this.form = data
			},

			save() {
				this.$refs['form'].validate(async (valid) => {
					if (!valid) return
					this.loading = true

					await this.$ut
						.api('rhinfo/room/betchAdd', {
							communityId: this.comm.id,
							dataTypeId: this.parentData.id,
							...this.form,
						})
						.finally(() => {
							this.loading = false
						})
					this.$baseMessage('操作成功！', 'success', 'ut-hey-message-success')
					this.$emit('fetchData')
					this.$emit('close')
				})
			},
			quickImage() {
				this.form.ext = 'jpg|jpeg|bmp|gif|webp|png|tiff'
			},
			getTypeList() {
				this.$ut
					.api('rhcustomer/datatype/listpg', {
						communityId: this.comm.id,
					})
					.then((res) => {
						this.typeList = res.data
					})
			},
			getRoomTypeList() {
				let list = []
				this.$ut
					.api('rhinfo/basic/roomType/listpg', {
						communityId: this.comm.id,
					})
					.then((res) => {
						if (res.code === 200) {
							list = res.data.info
							if (list && list.length > 0) {
								list.forEach((item) => {
									this.roomTypeList.push({
										label: item.name,
										value: item.id,
									})
								})
							}
						}
					})
			},
		},
	}
</script>
<style lang="scss" scoped>
	.color-box {
		display: inline-block;
		height: 32px;
		width: 150px;
		border: 1px solid #dcdfe6;
		padding-left: 20px;

		.color-box-text {
			color: #fff;
			mix-blend-mode: difference;
		}
	}
	.placeholder {
		height: 70px;
	}
</style>
