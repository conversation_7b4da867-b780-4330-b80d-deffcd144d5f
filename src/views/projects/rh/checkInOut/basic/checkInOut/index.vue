<template>
	<div class="table-container ut-body" :class="{ 'ut-fullscreen': fullscreen }">
		<form-list
			:loading="listLoading"
			:columns="columns"
			:height="height"
			:op-width="300"
			:op-fixed="true"
			:data-list="dataList"
			:select-window="selectWindow"
			:select-single="selectSingle"
			:show-list="showList"
			table-name="long-customer-type"
			@select="handleSelect"
			@fetchData="fetchData"
			@fullscreen="onFullscreen"
			@selectRows="onSelectRows"
		>
			<template slot="button">
				<el-button icon="el-icon-plus" type="primary" @click="handleAdd">添加</el-button>
				<el-button icon="el-icon-delete" type="danger" :disabled="!selectRows.length" @click="handleDelete($event)">删除</el-button>
			</template>
			<template #op="{ row }">
				<el-button type="text" @click="handleEdit(row)">编辑</el-button>
				<el-button type="text" >合同</el-button>
				<el-button type="text">评估报告</el-button>
				<template v-if="!row.outAudit">
					<el-button type="text" @click="handltAudit(row)">审核</el-button>
				</template>
				<el-button type="text" @click="handleDelete(row)">删除</el-button>
			</template>
			<template #cell="{ row, item }">
				<div v-if="item.prop === 'sex'">
                    <span v-if="row.sex == 1">男</span>
                    <span v-else>女</span>
				</div>
				<div v-else-if="item.prop === 'imgHead'">
					<div class="avater" >
						<img style="width: 100%;height:100%;" :src="$tools.showImg(row.imgHead,300)">
					</div>
				</div>
				<div v-else-if="item.prop === 'outAudit'">
					<span>{{ row.outAudit?'是':'否' }}</span>
				</div>
				<span v-else>{{ row[item.prop] }}</span>
			</template>
		</form-list>
		<el-dialog	v-if="dialogFormVisible" v-dialog-drag  :title="selectRow.id?'出院编辑':'出院添加'" :visible.sync="dialogFormVisible" append-to-body width="500px" :destroy-on-close="true" :close-on-click-modal="false" :close-on-press-escape="false"	>
			<edit :init-data="selectRow" @fetchData="fetchData" @close="dialogFormVisible=false" />
		</el-dialog>

	</div> 
</template>

<script>
import Edit from './edit'
import FormList from '@/views/components/form-list'
import { mapGetters } from 'vuex'
import Vue from 'vue'
export default {
	name: 'LongCustomerSource',
	components: {
		Edit,
		FormList,
	},
	props: {
		height: {
			type: Number,
			default: () => Vue.prototype.$baseTableHeight(1),
		},
		selectWindow: {
			type: Boolean,
			default: false,
		},
		selectSingle: {
			type: Boolean,
			default: false,
		},
		showList: {
			type: Boolean,
			default: false,
		},
	},
	data() {
		return {
			listLoading: false,
			fullscreen: false,
			columns: [
				{
					label: '姓名',
					align: 'left',
					prop: 'name',
					show: true,
					width:120
				},
				{
					label: '性别',
					align: 'center',
					prop: 'sex',
					show: true,
					width:80
				},
				{
					label: '电话',
					align: 'center',
					prop: 'phone',
					show: true,
					width:100
				},{
					label: '身份证号',
					align: 'center',
					prop: 'idcard',
					show: true,
					width:180
				},{
					label: '联系地址',
					align: 'center',
					prop: 'address',
					show: true,
					width:200,
					showTooltip:true,
				},{
					label: '头像',
					align: 'center',
					prop: 'imgHead',
					show: true,
					width:100,
				},{
					label: '生日',
					align: 'center',
					prop: 'birthday',
					show: true,
					width:100
				},{
					label: '出院日期',
					align: 'center',
					prop: 'checkOutDate',
					show: true,
					width:120
				},{
					label: '出院办理人',
					align: 'center',
					prop: 'checkOutUserName',
					show: true,
					width:100
				},{
					label: '出院备注',
					align: 'center',
					prop: 'checkOutRemark',
					show: true,
					width:200,
					showTooltip:true
				},{
					label: '计费起日期',
					align: 'center',
					prop: 'feeBegin',
					show: true,
					width:120
				},{
					label: '计费止日期',
					align: 'center',
					prop: 'feeEnd',
					show: true,
					width:120
				},{
					label: '房间名称',
					align: 'center',
					prop: 'roomName',
					show: true,
					width:120,
					showTooltip:true
				},{
					label: '床位名称',
					align: 'center',
					prop: 'allBedName',
					show: true,
					width:120,
					showTooltip:true
				},{
					label: '是否审核',
					align: 'center',
					prop: 'outAudit',
					show: true,
					width:100
				},{
					label: '审核时间',
					align: 'center',
					prop: 'outAuditTime',
					show: true,
					width:120
				}
			],
			dataList: {
				info: [],
				page: 0,
				record: 0,
			},
			page: {
				key: '',
				pageindex: 1,
				pagesize: 20,
			},
			selectRow: {},
			selectRows: [],
			dialogFormVisible: false,
		}
	},
	computed: {
		...mapGetters({
			comm: 'comm/comm',
		}),
	},
	created() {
		this.fetchData()
	},
	methods: {
		async fetchData(pageReq) {
			if (pageReq) this.page = pageReq
			this.listLoading = true
			const {data} = await this.$ut.api('rhcheckInOut/checkInOut/listpg', {
				communityId: this.comm.id,
                ...this.page,
            }).finally(()=>{this.listLoading=false})
            this.dataList=data
		},
		handleSelect(rows) {
			this.$emit('select', rows)
		},
		onFullscreen(v) {
			this.fullscreen = v
		},
		onSelectRows(rows) {
			this.selectRows = rows
		},
		handleAdd() {
			this.selectRow = {}
			this.dialogFormVisible = true
		},
		handleEdit(row) {
			this.selectRow = row
			this.dialogFormVisible = true
		},
		handleDelete(row) {
			let ids = []
			if (row.id) {
				ids = [row.id]
			} else {
				if (this.selectRows.length > 0) {
					ids = this.selectRows.map((item) => item.id)
				} else {
					this.$baseMessage('未选中任何行', 'error', 'ut-hey-message-error')
					return
				}
			}
			this.$baseConfirm('你确定要删除吗', null, async () => {
				this.$ut.api('rhcheckInOut/checkInOut/delete', {
					communityId: this.comm.id,
                    ids: ids,
                }).then(() => {
                    this.$baseMessage('操作成功', 'success', 'ut-hey-message-success')
                    this.fetchData()
                })
			})
		},
		handltAudit(row){
			let ids = []
			if (row.id) {
				ids = [row.id]
			} else {
				if (this.selectRows.length > 0) {
					ids = this.selectRows.map((item) => item.id)
				} else {
					this.$baseMessage('未选中任何行', 'error', 'ut-hey-message-error')
					return
				}
			}
			this.$baseConfirm('你确定要审核吗', null, async () => {
				this.$ut.api('rhcheckInOut/checkInOut/audit', {
					communityId: this.comm.id,
                    ids: ids,
                }).then(() => {
                    this.$baseMessage('操作成功', 'success', 'ut-hey-message-success')
                    this.fetchData()
                })
			})
		}
	},
}
</script>

<style lang="scss" scoped>
.color-box {
	display: flex;
	align-items: center;
	justify-content: center;

	.color-preview {
		width: 20px;
		height: 20px;
		margin-right: 10px;
        border-radius: 2px;
	}
}

.avater{
	width: 80px;
	height: 80px;
}
</style>
