<template>
	<div v-loading="loading">
		<el-form ref="form" label-width="130px" :model="form" :rules="rules" class="ut-form">
			<el-form-item label="项目名称：" prop="name">
				{{ form.name }}
			</el-form-item>
			<el-form-item label="次数：" prop="monthTimes">
				<el-input v-model="form.monthTimes" placeholder="请输入月服务次数" />
			</el-form-item>
		</el-form>
		<div class="ut-edit-footer">
			<el-button type="primary" @click="save">{{ lang('确定') }}</el-button>
			<el-button @click="$emit('close')">{{ lang('取消') }}</el-button>
		</div>
	</div>
</template>

<script>
import { mapGetters } from 'vuex'
import { lang } from '@/common/utils/i18n'
export default {
	name:'TimesEdit',
	props: {
		initData: {
			type: Object,
			default: () => ({}),
		},
	},
	data() {
		return {
			loading: false,
			form: {
				id:'',
				name: '',
				monthTimes: '',
			},
			rules: {
				monthTimes: [{ required: true, trigger: 'blur', message: '请输入月服务次数' }],
			},
		}
	},
	computed: {
		...mapGetters({
			comm: 'comm/comm',
		}),
	},
	beforeMount(){		
		this.getInfo()
	},
	mounted() {
	},
	methods: {
		lang,
		getInfo() {
			this.loading = true

			this.form.id = this.initData.id
			this.form.name = this.initData.name
			this.form.monthTimes = this.initData.monthTimes

			this.loading = false
		},
		save() {
			this.$refs['form'].validate(async (valid) => {
				if (!valid) return
				
				this.$emit('update-times',this.form)
				this.$emit('close')	
			})
		}
	},
}
</script>
<style lang="scss" scoped>
.color-box {
	display: inline-block;
	height: 32px;
	width: 150px;
	border: 1px solid #dcdfe6;
	padding-left: 20px;

	.color-box-text {
		color:#fff;
		mix-blend-mode: difference;
	}
}
.placeholder {
	height: 70px;
}
</style>
