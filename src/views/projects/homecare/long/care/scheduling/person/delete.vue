<template>
	<div v-loading="loading">
		<el-form ref="form" label-width="110px" :model="form" :rules="rules" class="ut-form">
			<el-form-item label="护理员：" >
				<span>{{ initData.name }}</span>
			</el-form-item>
			<el-form-item label="排班时间(起)：" prop="begin">
				<el-date-picker	v-model="form.begin" format="yyyy年MM月dd日" value-format="yyyy-MM-dd" type="date"/>
			</el-form-item>
            <el-form-item label="排班时间(止)：" prop="end">
				<el-date-picker	v-model="form.end" format="yyyy年MM月dd日" value-format="yyyy-MM-dd" type="date"/>
			</el-form-item>
            <div>
                注:以下几种情况无法删除
                <p>1.已经实际发生工作的；2.已经待定的。</p>
            </div>
		</el-form>
		<div class="ut-edit-footer">
			<el-button type="danger" @click="save">{{ lang('删除') }}</el-button>
			<el-button @click="$emit('close')">{{ lang('取消') }}</el-button>
		</div>


	</div>
</template>

<script>
import { mapGetters } from 'vuex'
import { lang } from '@/common/utils/i18n'
export default {
	components:{
	},
	props: {
		month:{
			type:String,
			default:'',
		},
		initData: {
			type: Object,
			default: () => ({}),
		},
	},
	data() {
		return {
			loading: false,
			form: {
				begin:'',
                end:'',
			},
			rules: {
				begin: [{ required: true, trigger: 'blur', message: '请指定排班时间起' }],
				end: [{ required: true, trigger: 'blur', message: '请指定排班时间止' }],
			},
		}
	},
	computed: {
		...mapGetters({
			comm: 'comm/comm',
		}),
	},
    watch:{
        month(v){
            console.log(v)
        }
    },
    created(){
        this.monthConvertDate(this.month)
    },
	beforeMount(){

	},
	mounted() {
	},
	methods: {
		lang,
		monthConvertDate(monthStr){
            var date
            if(monthStr){
                date=new Date(monthStr)
            }else{
                date=new Date()
            }
            var new_year = date.getFullYear(); // 取当前的年份（实际是输入日期的年份）  
            var month = date.getMonth(); // 获取月份（0-11）  
            var new_month = month + 1; // 将月份转换为可读的格式（1-12） 
            var firstDay = new Date(new_year, new_month - 1, 1); // 月份需要减1，因为getMonth()返回的是0-11  

            // 获取当月的最后一天  
            // 创建一个新的Date对象，年份和月份与firstDay相同，但日期是下个月的第0天（即上个月的最后一天）  
            var lastDay = new Date(new_year, new_month, 0).getDate();  
            // 格式化月份（总是两位数）  
            var mon = (new_month < 10 ? '0' : '') + new_month;  
            // 格式化日期（对于startDate，如果日期小于10，则添加前导零）  
            var startDateDay = ('0' + firstDay.getDate()).slice(-2); // 使用slice来确保总是两位数  
            this.form.begin = new_year + '-' + mon + '-' + startDateDay;  
            this.form.end = new_year + '-' + mon + '-' + lastDay;  
        },
       
		save() {
			this.$refs['form'].validate(async (valid) => {
				if (!valid) return
                this.$ut.api('homecarelong/care/scheduling/delete', {
                    communityId: this.comm.id,
                    attendantId:this.initData.id,
                    ...this.form,
                }).finally(()=>{this.loading=false})
                .then(()=>{
                    this.$baseMessage('排班删除操作成功！', 'success', 'ut-hey-message-success')
                    this.$emit('fetchData')
                    this.$emit('close')	
                })
			})
		},
	
	},
}
</script>
<style lang="scss" scoped>
.color-box {
	display: inline-block;
	height: 32px;
	width: 150px;
	border: 1px solid #dcdfe6;
	padding-left: 20px;

	.color-box-text {
		color:#fff;
		mix-blend-mode: difference;
	}
}
.placeholder {
	height: 70px;
}
</style>
