<template>
	<form-tree-list :tree-width="200" :class="{ 'ut-fullscreen': fullscreen }">
		<template #tree>
			<tree :show-tool="false" @node-click="nodeClick"/> 
		</template>
		<template #list>
			<scheduling-list :parent-data="treeNode" />
		</template>
	</form-tree-list>


</template>

<script>

import Vue from 'vue'
import FormTreeList from '@/views/components/form-tree-list'
// import AttendantList from '../../../transaction/customer/attendant-list.vue'
//import CustomerList from './customer-list.vue'
import SchedulingList from './list.vue'
import Tree from './tree.vue'
export default {
	name:'LongSchedulingCustomer',
	components: {
		FormTreeList,
		// AttendantList,
		SchedulingList,
		Tree,
	},
	props: {
		selectWindow: {
			type: Boolean,
			default: false,
		},
		selectSingle: {
			type: Boolean,
			default: false,
		},
		height:{
			type:[Number,String],
			default: () => Vue.prototype.$baseTableHeight(1),
		}
	},
	data() {
		return {
			fullscreen: false,
			treeNode:{},
			selAttendant:{},
			customerSchedulingVisible:false,
		}
	},
	computed: {},
	created() {},
	methods: {
		nodeClick(node) {
			this.treeNode=node
		},

		// handleSelect(rows){
		// 	this.$emit('select',rows)
		// },
		// handleAttendantSelect(rows){
		// 	if(rows && rows.length){
		// 		this.selAttendant=rows[0]
		// 	}
		// }

	},
}
</script>
<style>
.ut-layout-column,.ut-app-main,.ut-main{
	height: 100%;
}

section{
	background: none !important;
	height: calc(100% - 40px);
	padding-bottom: 4px;
}
</style>
<style lang="scss" scoped>
:deep(.el-card__body){
	height: 100%;
}
</style>