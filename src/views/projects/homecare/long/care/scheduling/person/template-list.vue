<template>
	<div class="table-container ut-body" :class="{ 'ut-fullscreen': fullscreen }">
		<form-list
			:loading="listLoading"
			:columns="columns"
			:height="height"
			:op-width="100"
			:data-list="dataList"
			:select-window="selectWindow"
			:select-single="selectSingle"
			:show-list="showList"
			:page-size="page.pagesize"
			table-name="long-basic-project"
			@select="handleSelect"
			@fetchData="fetchData"
			@fullscreen="onFullscreen"
			@selectRows="onSelectRows"
		>
			<template slot="button">
				<el-button icon="el-icon-plus" type="primary" @click="handleAdd">添加</el-button>
				<el-button icon="el-icon-delete" type="danger" :disabled="!selectRows.length" @click="handleDelete($event)">删除</el-button>
			</template>
			<template #op="{ row }">
				<el-button type="text" @click="handleEdit(row)">编辑</el-button>
				<el-button type="text" @click="handleDelete(row)">删除</el-button>
			</template>
			<template #cell="{ row, item }">
				<div v-if="item.prop === 'color'" class="color-box">
					<span class="color-preview" :style="{ backgroundColor: row.color }"></span>
					{{ row[item.prop] }}
				</div>
                <div v-else-if="item.prop === 'valid'">
                    <span v-if="row.valid"><i class="el-icon-check" /></span>
                    <span v-else>未启用</span>
				</div>
				<span v-else>{{ row[item.prop] }}</span>
			</template>
		</form-list>
	</div>
</template>

<script>
import FormList from '@/views/components/form-list'
import { mapGetters } from 'vuex'
import Vue from 'vue'
export default {
	name: 'LongCustomerSource',
	components: {
		FormList,
	},
	props: {
		height: {
			type: [String,Number],
			default: () => Vue.prototype.$baseTableHeight(1),
		},
		selectWindow: {
			type: Boolean,
			default: false,
		},
		selectSingle: {
			type: Boolean,
			default: false,
		},
		showList: {
			type: Boolean,
			default: false,
		},
		attendantId:{
			type:String,
			default:'',
		}
	},
	data() {
		return {
			listLoading: false,
			fullscreen: false,
			columns: [
				{
					label: '创建时间',
					align: 'center',
					prop: 'createTime',
					width: 160,
					show: true,
				},
				{
					label: '排班法',
					align: 'center',
					prop: 'weekCount',
					width:100,
					show: true,
				},
				{
					label: '参考日期',
					align: 'center',
					prop: 'referenceDate',
					width:100,
					show: true,
				},
				{
					label: '客户数量',
					align: 'center',
					prop: 'customerCount',
					width:80,
					show: true,
				},
				{
					label: '客户',
					align: 'left',
					prop: 'customerNames',
					minWidth:200,
					show: true,
				},
			],
			dataList: {
				info: [],
				page: 0,
				record: 0,
			},
			page: {
				key: '',
				pageindex: 1,
				pagesize: 100,
			},
			selectRow: {},
			selectRows: [],
			dialogFormVisible: false,
		}
	},
	computed: {
		...mapGetters({
			comm: 'comm/comm',
		}),
	},
	created() {
		this.fetchData()
	},
	methods: {
		async fetchData(pageReq) {
			if (pageReq) this.page = pageReq
			this.listLoading = true
			const {data} = await this.$ut.api('homecarelong/care/plan/setting/template/listpg', {
				communityId: this.comm.id,
				attendantId:this.attendantId,
                ...this.page,
            }).finally(()=>{this.listLoading=false})
            this.dataList=data
		},
		handleSelect(rows) {
			this.$emit('select', rows)
		},
		onFullscreen(v) {
			this.fullscreen = v
		},
		onSelectRows(rows) {
			this.selectRows = rows
		},
		handleAdd() {
			this.selectRow = {}
			this.dialogFormVisible = true
		},
		handleEdit(row) {
			this.selectRow = row
			this.dialogFormVisible = true
		},
		handleDelete() {
			// let ids = []
			// if (row.id) {
			// 	ids = [row.id]
			// } else {
			// 	if (this.selectRows.length > 0) {
			// 		ids = this.selectRows.map((item) => item.id)
			// 	} else {
			// 		this.$baseMessage('未选中任何行', 'error', 'ut-hey-message-error')
			// 		return
			// 	}
			// }
			// this.$baseConfirm('你确定要删除吗', null, async () => {
			// 	this.$ut.api('homecarelong/basic/project/delete', {
			// 		communityId: this.comm.id,
			// 		module:'long',
            //         ids: ids,
            //     }).then(() => {
            //         this.$baseMessage('操作成功', 'success', 'ut-hey-message-success')
            //         this.fetchData()
            //     })
			// })
		},
	},
}
</script>

<style lang="scss" scoped>
.color-box {
	display: flex;
	align-items: center;
	justify-content: center;

	.color-preview {
		width: 20px;
		height: 20px;
		margin-right: 10px;
        border-radius: 2px;
	}
}

</style>
