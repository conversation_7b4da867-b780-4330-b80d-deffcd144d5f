<template>
	<div v-loading="loading">
		<el-form ref="form" label-width="130px" :model="form" :rules="rules" class="ut-form">
			<el-form-item label="护理员：" prop="attendant">
				{{ attendant?attendant.name:'' }}
			</el-form-item>
			<el-form-item label="排班模板：" prop="templateTitle">
				<el-input v-model.trim="form.templateTitle"  placeholder="请选择排班模板" @focus="templateVisible = true" >
					<template slot="append">
						<i class="el-icon-search" @click="templateVisible = true"></i>
					</template>
				</el-input>
			</el-form-item>
			<el-form-item label="起日期：" prop="begin">
				<el-date-picker	v-model="form.begin" format="yyyy年MM月dd日" value-format="yyyy-MM-dd" type="date" placeholder="选择排班起日期"/>
			</el-form-item>
			<el-form-item label="止日期：" prop="end">
				<el-date-picker	v-model="form.end" format="yyyy年MM月dd日" value-format="yyyy-MM-dd" type="date" placeholder="选择排班止日期"/>
			</el-form-item>
		</el-form>
		<div class="ut-edit-footer">
			<el-button type="primary" @click="save">{{ lang('确定') }}</el-button>
			<el-button @click="$emit('close')">{{ lang('取消') }}</el-button>
		</div>

		
		<ut-modal v-model="templateVisible" title="选择模板"  width="800px">
			<template-list :select-window="true" :select-single="true" :attendant-id="attendant.id" height="75%" @select="handleSelectTemplate" @close="templateVisible = false" />
		</ut-modal>
	</div>
</template>

<script>
import { mapGetters } from 'vuex'
import { lang } from '@/common/utils/i18n'
import TemplateList from './template-list.vue';
export default {
	name:'CustomerSchedulingEdit',
	components:{
		TemplateList	
	},
	props: {
		attendant:{
			type:Object,
			default:()=>{},
		},
		initData: {
			type: Object,
			default: () => ({}),
		},
	},
	data() {
		return {
			loading: false,
			form: {
				id: '',
				customerId: '',
				workDate: '',
				customerName:'',
			},
			rules: {
				templateTitle: [{ required: true, trigger: 'blur', message: '请选择模板' }],
				begin: [{ required: true, trigger: 'blur', message: '请指定开始日期' }],
				end: [{ required: true, trigger: 'blur', message: '请指定截止日期' }],
			},
			templateVisible:false,
		}
	},
	computed: {
		...mapGetters({
			comm: 'comm/comm',
		}),
	},
	beforeMount(){
		//this.getInfo()
	},
	mounted() {
	},
	methods: {
		lang,
		async getInfo() {
			if (!this.initData.id) return
			this.loading = true
			const { data } = await this.$ut.api('homecarelong/care/head/scheduling/customer/dateInfo', {
				communityId: this.comm.id,
				module:'long',
				id: this.initData.id,
			}).finally(()=>{this.loading=false})
			this.form = data
		},
		save() {
			this.$refs['form'].validate(async (valid) => {
				if (!valid) return
				this.loading = true
				await this.$ut.api('homecarelong/care/head/scheduling/customer/attendantBuild', {
				  	communityId: this.comm.id,
					module:'long',
					...this.form,
				}).finally(()=>{this.loading=false})
				this.$baseMessage('操作成功！', 'success', 'ut-hey-message-success')
				this.$emit('fetchData')
				this.$emit('close')	
			})
		},
		handleSelectTemplate(rows) {
			this.customerVisible = false
			if (rows && rows[0]) {
				this.form.templateId = rows[0].id
				this.form.templateTitle = rows[0].createTime
			}
		},
	},
}
</script>
<style lang="scss" scoped>
.color-box {
	display: inline-block;
	height: 32px;
	width: 150px;
	border: 1px solid #dcdfe6;
	padding-left: 20px;

	.color-box-text {
		color:#fff;
		mix-blend-mode: difference;
	}
}
.placeholder {
	height: 70px;
}
</style>
