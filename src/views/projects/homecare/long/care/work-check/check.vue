<template>
    <div v-loading="loading">
        <div class="box-wrapper ut-body" :style="{ height: `${height}px` }">
            <el-row type="flex">
                <el-col :span="10">
                    <div class="customer-info">
                        <el-descriptions :column="9" border>
                            <el-descriptions-item span="3" label="客户姓名">{{ form.name }}</el-descriptions-item>
                            <el-descriptions-item span="3" label="性别">
                                <span v-if="form.sex === 1">男</span>
                                <span v-else-if="form.sex === 2">女</span>
                            </el-descriptions-item>
                            <el-descriptions-item span="3" label="手机号">{{ form.phone }}</el-descriptions-item>
                            <el-descriptions-item span="3" label="证件号码">{{ form.idcard }}</el-descriptions-item>
                            <el-descriptions-item span="3" label="分组名称">{{ form.groupName }}</el-descriptions-item>
                            <el-descriptions-item span="3" label="客户员">{{ form.attendantName }}</el-descriptions-item>
                            <el-descriptions-item span="3" label="护理日期">{{ form.workDate }}</el-descriptions-item>
                            <el-descriptions-item span="3" label="排班时长">{{ form.schedulingDuration }}</el-descriptions-item>
                            <el-descriptions-item span="3" label="实际时长">{{ form.totalDuration }}</el-descriptions-item>
                            <el-descriptions-item span="3" label="签到时间">{{ form.checkInTime }}</el-descriptions-item>
                            <el-descriptions-item span="3" label="签退时间">{{ form.checkOutTime }}</el-descriptions-item>
                        </el-descriptions>
                    </div>
                    <div class="project-section">
                        <project-list :projects="form.projects" :parent-data="childData" @update:projects="updateProjects" @reset="resetProjects" />
                    </div>
                </el-col>
                <el-col :span="14">
                    <data-set :datas="dataDetail" :style="{ height: baseTableHeight(1) }" />
                    <el-form class="check-section">
                        <el-form-item class="check-item" label="校对结果：" required>
                            <div class="proofread-container">
                                <div class="main-option">
                                    <el-radio-group v-model="form.proofreadError">
                                        <el-radio :label="false">无异常</el-radio>
                                        <el-radio :label="true">有异常</el-radio>
                                    </el-radio-group>
                                </div>
                                <div v-if="form.proofreadError" class="error-types">
                                    <div class="error-types-options">
                                        <div v-for="option in errorTypeOptions" :key="option" class="error-type-item">
                                            <div class="error-type-label">{{ option }}：</div>
                                            <el-radio-group
                                                :value="errorTypeStates[option]"
                                                class="error-type-buttons"
                                                @input="updateErrorTypeState(option, $event)"
                                            >
                                                <el-radio-button :label="false">正确</el-radio-button>
                                                <el-radio-button :label="true">错误</el-radio-button>
                                            </el-radio-group>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </el-form-item>
                    </el-form>
                </el-col>
            </el-row>
        </div>
        <div class="ut-edit-footer">
            <div class="surplus-info">
                <div v-if="form.suplus > 0" class="surplus-text">
                    剩余
                    <span class="surplus-count">{{ form.suplus }}</span>
                    份未校对
                </div>
                <div v-else class="surplus-text">暂无剩余校对任务</div>
            </div>
            <el-button type="primary" :loading="loading" @click="save">保存</el-button>
            <el-button @click="$emit('close')">关闭</el-button>
        </div>
    </div>
</template>

<script>
import { mapGetters } from 'vuex'
import ProjectList from './project-list.vue'
import Vue from 'vue'
import DataSet from './data-set.vue'

export default {
    components: {
        DataSet,
        ProjectList,
    },
    props: {
        height: {
            type: Number,
            default: () => Vue.prototype.$baseTableHeight(-1),
        },
        parentData: {
            type: Object,
            default: () => ({}),
        },
        month: {
            type: String,
            default: '',
        },
    },
    data() {
        return {
            loading: false,
            taskData: {},
            form: {
                id: '',
                workDate: '',
                groupName: '',
                attendantId: '',
                attendantName: '',
                customerId: '',
                name: '',
                sex: 0,
                phone: '',
                idcard: '',
                address: '',
                projects: [],
                datas: [],
                suplus: 0,
                proofreadError: false,
                proofreadErrorRemark: '',
            },
            originalProjects: [],
            errorTypeOptions: ['签字表时间', '签字表项目', '项目时长'],
            errorTypeStates: {}, // 记录每个异常类型的状态：null(未选择)、true(错误)、false(正确)
        }
    },
    computed: {
        ...mapGetters({
            comm: 'comm/comm',
        }),
        childData() {
            return {
                workId: this.form.id || this.parentData.id,
                customerId: this.form.customerId || this.parentData.customerId,
                workDate: this.form.workDate || this.parentData.workDate,
            }
        },
        selectedErrors: {
            get() {
                if (!this.form.proofreadError) {
                    return []
                }
                return this.form.proofreadErrorRemark?.trim().split('、') ?? []
            },
            set(val) {
                this.form.proofreadErrorRemark = val?.filter(Boolean).join('、') ?? ''
            },
        },
        dataDetail() {
            if (!this.form.datas?.length) return null
            const titles = []
            const files = []
            this.form.datas.forEach((data) => {
                titles.push(data.title)
                data.details?.forEach((detail) => {
                    files.push(...(detail.files || []))
                })
            })
            return {
                title: titles.join('、'),
                files,
            }
        },
    },
    mounted() {
        const workId = this.parentData?.id
        this.loadData(workId)
    },
    methods: {
        baseTableHeight(i) {
            return `${this.$baseTableHeight(i)}px`
        },
        async loadData(workId = null) {
            this.loading = true
            try {
                const { data } = await this.$ut.api('homecarelong/care/work/proofread/task', {
                    communityId: this.comm.id,
                    workId,
                    month: this.month,
                })
                this.updateData(data)
            } finally {
                this.loading = false
            }
        },
        updateData(data) {
            this.taskData = data
            this.form = {
                ...this.form,
                ...data,
                projects: data.projects || [],
                datas: data.datas || [],
                suplus: data.suplus || 0,
            }
            this.originalProjects = JSON.parse(JSON.stringify(data.projects || []))
            // 初始化异常类型状态
            this.initErrorTypeStates()
        },
        initErrorTypeStates() {
            // 根据现有的 proofreadErrorRemark 初始化状态
            const errorStates = {}
            this.errorTypeOptions.forEach(option => {
                errorStates[option] = null // 默认未选择
            })

            if (this.form.proofreadError && this.form.proofreadErrorRemark) {
                const selectedErrors = this.form.proofreadErrorRemark.trim().split('、')
                selectedErrors.forEach(error => {
                    if (this.errorTypeOptions.includes(error)) {
                        errorStates[error] = true // 标记为错误
                    }
                })
            }

            this.errorTypeStates = errorStates
        },
        updateErrorTypeState(option, value) {
            this.$set(this.errorTypeStates, option, value)
            // 同步更新 proofreadErrorRemark
            this.syncErrorRemark()
        },
        syncErrorRemark() {
            // 收集所有标记为"错误"的异常类型
            const errorTypes = this.errorTypeOptions.filter(option =>
                this.errorTypeStates[option] === true
            )
            this.form.proofreadErrorRemark = errorTypes.join('、')
        },
        updateProjects(newProjects) {
            const projectMap = new Map()
            newProjects.forEach((project) => {
                projectMap.set(project.projectId, project)
            })
            this.form.projects = Array.from(projectMap.values())
        },
        resetProjects() {
            this.form.projects = JSON.parse(JSON.stringify(this.originalProjects))
        },
        async save() {
            if (this.form.proofreadError === null) {
                this.$baseMessage('请选择是否有异常', 'error')
                return
            }
            if (this.form.proofreadError) {
                // 检查是否所有异常类型都已选择
                const unselectedTypes = this.errorTypeOptions.filter(option =>
                    this.errorTypeStates[option] === null || this.errorTypeStates[option] === undefined
                )
                if (unselectedTypes.length > 0) {
                    this.$baseMessage(`请为所有异常类型选择正确或错误：${unselectedTypes.join('、')}`, 'error')
                    return
                }
            }
            this.loading = true
            try {
                const params = {
                    communityId: this.comm.id,
                    workId: this.form.id,
                    month: this.month,
                    projectIds: this.form.projects?.map((p) => p.projectId) || [],
                    proofreadError: this.form.proofreadError,
                }
                if (this.form.proofreadError) {
                    params.proofreadErrorRemark = this.form.proofreadErrorRemark
                }
                const { data } = await this.$ut.api('homecarelong/care/work/proofread/saveTask', params)
                this.$baseMessage('保存成功！', 'success', 'ut-hey-message-success')
                this.$emit('fetchData')
                data ? this.updateData(data) : this.$emit('close')
            } finally {
                this.loading = false
            }
        },
    },
}
</script>

<style lang="scss" scoped>
.box-wrapper {
    overflow-y: auto;
}

.customer-info {
    padding-bottom: 8px;
    line-height: 1.5;
}

.project-section {
    .section-header {
        display: flex;
        align-items: center;
        gap: 10px;

        .section-title {
            font-weight: bold;
            font-size: 16px;
        }
    }
}

.check-section {
    margin-top: 10px;
    display: flex;
    justify-content: center;

    .check-item {
        display: flex;
        align-items: flex-start;
        margin-bottom: 0 !important;

        .proofread-container {
            min-width: 300px;

            .main-option {
                .el-radio-group {
                    .el-radio {
                        margin-bottom: 0;
                    }
                }
            }

            .error-types {
                padding-left: 20px;
                overflow: hidden;

                .error-types-options {
                    display: flex;
                    flex-direction: column;
                    gap: 15px;

                    .error-type-item {
                        display: flex;
                        align-items: center;
                        gap: 10px;

                        .error-type-label {
                            min-width: 80px;
                            font-weight: 500;
                            color: #606266;
                        }

                        .error-type-buttons {
                            .el-radio-button {
                                margin-right: 0;

                                &:first-child .el-radio-button__inner {
                                    border-radius: 4px 0 0 4px;
                                }

                                &:last-child .el-radio-button__inner {
                                    border-radius: 0 4px 4px 0;
                                }
                            }
                        }
                    }
                }
            }
        }
    }
}

.surplus-info {
    display: inline-block;
    margin-right: 20px;

    .surplus-text {
        font-size: 14px;

        .surplus-count {
            font-weight: bold;
            color: #f56c6c;
        }
    }
}
</style>
