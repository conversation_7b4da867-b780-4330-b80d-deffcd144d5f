<template>
    <div v-loading="loading" class="ut-body">
        <el-form ref="form" label-width="130px" :model="form" :rules="rules" class="=ut-form">
            <div class="customer-info">
                <el-descriptions :column="9" border>
                    <el-descriptions-item span="3" label="客户姓名">{{ form.name }}</el-descriptions-item>
                    <el-descriptions-item span="3" label="性别">
                        <span v-if="form.sex === 1">男</span>
                        <span v-else-if="form.sex === 2">女</span>
                    </el-descriptions-item>
                    <el-descriptions-item span="3" label="手机号">{{ form.phone }}</el-descriptions-item>
                    <el-descriptions-item span="3" label="证件号码">{{ form.idcard }}</el-descriptions-item>
                    <el-descriptions-item span="3" label="分组名称">{{ form.groupName }}</el-descriptions-item>
                    <el-descriptions-item span="3" label="客户员">{{ form.attendantName }}</el-descriptions-item>
                    <el-descriptions-item span="3" label="护理日期">{{ form.workDate }}</el-descriptions-item>
                    <el-descriptions-item span="3" label="排班时长">{{ form.schedulingDuration }}</el-descriptions-item>
                    <el-descriptions-item span="3" label="实际时长">{{ form.totalDuration }}</el-descriptions-item>
                    <el-descriptions-item span="3" label="签到时间">{{ form.checkInTime }}</el-descriptions-item>
                    <el-descriptions-item span="3" label="签退时间">{{ form.checkOutTime }}</el-descriptions-item>
                    <el-descriptions-item span="3" label="校对人">{{ form.lastManualUserName }}</el-descriptions-item>
                    <el-descriptions-item span="3" label="校对时间">{{ form.lastManualTime }}</el-descriptions-item>
                    <el-descriptions-item span="3" label="校对异常信息">
                        <span style="color: rgb(245, 108, 108)">{{ form.proofreadErrorRemark }}</span>
                    </el-descriptions-item>
                </el-descriptions>
            </div>
            <el-tabs v-model="tabName" type="border-card">
                <el-tab-pane name="projects">
                    <span slot="label">服务项目</span>
                    <div v-if="form.id">
                        <project-list :projects="form.projects" :parent-data="childData" can-edit :height="height1" @update:projects="updateProjects" />
                    </div>
                </el-tab-pane>
                <el-tab-pane name="dataSet">
                    <span slot="label">服务资料</span>
                    <div v-if="form.id">
                        <data-set :datas="datas" :height="height2" @save="handleDataSetSave" />
                    </div>
                </el-tab-pane>
            </el-tabs>
        </el-form>

        <div class="ut-edit-footer">
            <el-button type="danger" @click="judan">{{ lang('拒单') }}</el-button>
            <el-button type="primary" @click="save">{{ lang('确定') }}</el-button>
            <el-button @click="$emit('close')">{{ lang('取消') }}</el-button>
        </div>

        <ut-modal v-model="judanVisible" title="拒单" width="400px">
            <judan :parent-data="judanData" @fetchData="handleJudan" @close="judanVisible = false" />
        </ut-modal>
    </div>
</template>

<script>
import { mapGetters } from 'vuex'
import { lang } from '@/common/utils/i18n'
import projectList from '../work-edit/project-list'
import UploadMixins from '../../../components/upload.mixins.js'
import dataSet from '../work-edit/data-set.vue'
import Judan from './judan.vue'

export default {
    components: {
        Judan,
        projectList,
        dataSet,
    },
    mixins: [UploadMixins],
    props: {
        parentData: {
            type: Object,
            default: () => ({}),
        },
        month: {
            type: String,
            default: '',
        },
    },
    data() {
        return {
            height1: this.$baseTableHeight(8),
            height2: this.$baseTableHeight(7),
            loading: false,
            tabName: 'projects',
            form: {},
            rules: {
                checkInTime: [{ required: true, trigger: 'blur', message: '请选择签到时间' }],
                checkOutTime: [{ required: true, trigger: 'blur', message: '请选择签退时间' }],
            },
            customer: {
                id: '',
                name: '',
                phone: '',
                idcard: '',
                address: '',
            },
            datas: [],
            judanVisible: false,
            judanData: {},
        }
    },
    computed: {
        ...mapGetters({
            comm: 'comm/comm',
        }),
        childData() {
            return {
                workId: this.parentData.id,
                customerId: this.form.id,
                workDate: this.form.workDate,
            }
        },
    },
    created() {},
    async beforeMount() {
        this.getInfo()
    },
    destroyed() {},
    methods: {
        lang,
        async getInfo() {
            if (!this.parentData.id) return
            this.loading = true
            const params = {
                communityId: this.comm.id,
                workId: this.parentData.id,
                month: this.month,
            }
            if (this.month) params.month = this.month
            const { data } = await this.$ut.api('homecarelong/care/work/proofread/task', params).finally(() => {
                this.loading = false
            })
            this.$set(this, 'form', {
                ...this.form,
                ...data,
                projects: data.projects?.map((item) => ({
                    ...item,
                    id: item.projectId,
                    // _id 为关联 id
                    _id: item.id,
                })),
            })
            this.datas = data.datas
        },
        updateProjects(newProjects) {
            const uniqueProjects = []
            const idSet = new Set()
            newProjects.forEach((project) => {
                if (!idSet.has(project.id)) {
                    idSet.add(project.id)
                    uniqueProjects.push(project)
                }
            })
            this.$set(this.form, 'projects', uniqueProjects)
        },
        handleDataSetSave(v) {
            this.form.datas = v
        },
        save() {
            this.$refs['form'].validate(async (valid) => {
                if (!valid) return
                this.loading = true
                await this.$ut
                    .api('homecarelong/care/work/proofread/error/save', {
                        communityId: this.comm.id,
                        workId: this.parentData.id,
                        projectIds: this.form.projects?.map((item) => item.id),
                        datas: this.form.datas,
                    })
                    .finally(() => {
                        this.loading = false
                    })
                this.$baseMessage('操作成功！', 'success', 'ut-hey-message-success')
                this.$emit('fetchData')
                this.$emit('close')
            })
        },
        judan() {
            this.judanVisible = true
            this.judanData = {
                workId: this.parentData.id,
                reason: this.form.proofreadErrorRemark,
            }
        },
        handleJudan() {
            this.$emit('fetchData')
            this.$emit('close')
        },
    },
}
</script>
<style lang="scss" scoped>
.sign {
    padding: 20px;

    .form-item {
        display: flex;
        font-size: 16px;
        margin-bottom: 20px;

        .label {
            width: 100px;
            color: #666;
            line-height: 32px;
        }

        .value {
            color: #333;

            .image {
                width: 148px;
                height: 148px;
                border-radius: 5px;
                overflow: hidden;
                display: flex;
                align-items: center;
                justify-content: center;
                background: #f2f2f2;
            }
        }
    }
}

.customer-info {
    padding-bottom: 8px;
    line-height: 1.5;
}

.project-wrapper {
    display: flex;
    flex-direction: row;
    padding-bottom: 12px;

    .title {
        width: 130px;
        text-align: right;
        padding-right: 4px;
    }
}

.project-box {
    display: flex;
    flex-direction: column;
    flex-wrap: wrap;
    max-height: 330px;
    min-height: 200px;
    overflow: auto;
    flex: 1;
    border: 1px solid #f2f2f2;
    padding: 8px;
    font-size: 16px;
}

.project-item {
    min-width: 200px;
    padding: 0 15px;
    line-height: 2;
}

.select-window {
    :deep(.ut-list) {
        .el-card__body {
            height: auto !important;
        }
    }

    :deep(.ut-tree) {
        .el-card {
            height: 100%;
        }

        .el-card__body {
            height: auto !important;
        }
    }

    :deep(.ut-tree-list) {
        padding: 0;
    }
}

.form-item {
    .value.location {
        font-size: 14px;
        line-height: 30px;
    }
}
</style>
